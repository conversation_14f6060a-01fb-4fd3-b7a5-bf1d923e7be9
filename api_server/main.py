#!/usr/bin/env python3
"""
🚀 FastAPI Bridge Server - PRISM DEX Arbitrage Dashboard

Connects sophisticated Python backend engines to React TypeScript frontend.
Port 8001 (SurrealDB occupies 8000).
"""

from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, List, Optional, Any
import asyncio
import json
import uuid
from datetime import datetime, timezone, timedelta
import time
import logging
import os
from pathlib import Path

# Import WebSocket manager
try:
    from websocket_manager import WebSocketManager
    logging.info("✅ WebSocketManager imported successfully")
except ImportError as e:
    logging.warning(f"⚠️ WebSocketManager import warning: {e}")
    # Create a simple fallback WebSocket manager
    class WebSocketManager:
        def __init__(self):
            self.active_connections = []
        
        async def connect(self, websocket):
            self.active_connections.append(websocket)
        
        async def disconnect(self, websocket):
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
        
        async def send_to_connection(self, websocket, message):
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logging.error(f"Failed to send message: {e}")
        
        async def handle_client_message(self, websocket, data):
            # Simple echo for now
            await self.send_to_connection(websocket, {"type": "echo", "data": data})
        
        async def send_heartbeat(self):
            for connection in self.active_connections:
                try:
                    await connection.send_text(json.dumps({"type": "heartbeat"}))
                except Exception as e:
                    logging.error(f"Heartbeat failed: {e}")
        
        def get_connection_stats(self):
            return {"active_connections": len(self.active_connections)}
        
        async def broadcast(self, message):
            """Broadcast message to all connected clients"""
            for connection in self.active_connections:
                try:
                    await self.send_to_connection(connection, message)
                except Exception as e:
                    logger.error(f"Failed to broadcast to connection: {e}")
                    # Remove dead connection
                    if connection in self.active_connections:
                        self.active_connections.remove(connection)

# Import existing sophisticated backend engines with proper error handling
ArbitrageFinder = None
OpportunityExecutor = None
LoggingManager = None
QuoteCircuitBreaker = None
DependencyManager = None

try:
    from engines.arbitrage.arbitrage_finder_service import ArbitrageFinder
    logging.info("✅ ArbitrageFinder imported successfully")
except ImportError as e:
    logging.warning(f"⚠️ ArbitrageFinder import warning: {e}")

try:
    from engines.mev_engine.opportunity_executor import OpportunityExecutor
    logging.info("✅ OpportunityExecutor imported successfully")
except ImportError as e:
    logging.warning(f"⚠️ OpportunityExecutor import warning: {e}")

try:
    from infrastructure.observability.logging_manager import AsyncTradingLogger as LoggingManager
    logging.info("✅ LoggingManager imported successfully")
except ImportError as e:
    logging.warning(f"⚠️ LoggingManager import warning: {e}")

try:
    from providers.dex.shared.quote_retry_system import QuoteCircuitBreaker
    logging.info("✅ QuoteCircuitBreaker imported successfully")
except ImportError as e:
    logging.warning(f"⚠️ QuoteCircuitBreaker import warning: {e}")

try:
    from infrastructure.dependency_resilience_manager import AuthenticDependencyResilienceManager as DependencyManager
    logging.info("✅ DependencyManager imported successfully")
except ImportError as e:
    logging.warning(f"⚠️ DependencyManager import warning: {e}")

# Alternative import paths for LoggingManager
if not LoggingManager:
    try:
        from infrastructure.logging_manager import LoggingManager
        logging.info("✅ LoggingManager imported from alternative location")
    except ImportError:
        logging.warning("⚠️ LoggingManager not available from any location")

# Pydantic models for API responses
from pydantic import BaseModel
from typing import Union

# Add authentication models
class LoginRequest(BaseModel):
    email: str
    password: str

class SignupRequest(BaseModel):
    email: str
    password: str
    name: str

class LoginResponse(BaseModel):
    success: bool
    data: dict
    message: str

class User(BaseModel):
    id: str
    email: str
    role: str
    name: str

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# FastAPI application
app = FastAPI(
    title="PRISM DEX Arbitrage API",
    description="Bridge between React frontend and Python trading backend",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS configuration for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Vite dev server (primary)
        "http://localhost:5174",  # Vite dev server (secondary)
        "http://localhost:5175",  # Vite dev server (tertiary)
        "http://localhost:3000",  # Alternative React dev server
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5174",
        "http://127.0.0.1:5175",
        "http://127.0.0.1:3000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize WebSocket manager
websocket_manager = WebSocketManager()

# Global backend engine variables (graceful degradation)
arbitrage_finder = None
opportunity_executor = None
logging_manager = None
circuit_breaker = None

@app.on_event("startup")
async def startup_event():
    """Initialize backend engines on startup"""
    global arbitrage_finder, opportunity_executor, logging_manager, circuit_breaker
    
    try:
        logger.info("🚀 Initializing FastAPI bridge server...")
        
        # Initialize existing sophisticated backend components
        try:
            arbitrage_finder = ArbitrageFinder()
            logger.info("✅ ArbitrageFinder initialized")
        except Exception as e:
            logger.warning(f"⚠️ ArbitrageFinder initialization failed: {e}")
            
        try:
            opportunity_executor = OpportunityExecutor() 
            logger.info("✅ OpportunityExecutor initialized")
        except Exception as e:
            logger.warning(f"⚠️ OpportunityExecutor initialization failed: {e}")
            
        try:
            logging_manager = LoggingManager()
            logger.info("✅ LoggingManager initialized")
        except Exception as e:
            logger.warning(f"⚠️ LoggingManager initialization failed: {e}")
            
        try:
            circuit_breaker = QuoteCircuitBreaker()
            logger.info("✅ QuoteCircuitBreaker initialized")
        except Exception as e:
            logger.warning(f"⚠️ QuoteCircuitBreaker initialization failed: {e}")
        
        logger.info("🎯 FastAPI bridge server ready at http://localhost:8001")
        logger.info("📊 Swagger UI available at http://localhost:8001/docs")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize backend engines: {e}")
        # Don't raise - allow server to start in degraded mode


# Schema mapping functions
def map_backend_opportunity_to_frontend(backend_opp) -> Dict[str, Any]:
    """Map backend ArbitrageOpportunity to frontend schema"""
    try:
        return {
            "id": str(uuid.uuid4()),
            "pair": {
                "baseToken": {
                    "address": getattr(backend_opp, 'token_mint', 'unknown'),
                    "symbol": getattr(backend_opp, 'token_symbol', 'TOKEN'),
                    "name": getattr(backend_opp, 'token_name', 'Token'),
                    "decimals": 9
                },
                "quoteToken": {
                    "address": "So11111111111111111111111111111111111111112",
                    "symbol": "SOL", 
                    "name": "Solana",
                    "decimals": 9
                },
                "address": getattr(backend_opp, 'token_mint', 'unknown')
            },
            "buyExchange": str(getattr(backend_opp, 'source_dex', 'UNKNOWN')),
            "sellExchange": str(getattr(backend_opp, 'target_dex', 'UNKNOWN')),
            "buyPrice": getattr(backend_opp, 'buy_price', 0.0),
            "sellPrice": getattr(backend_opp, 'sell_price', 0.0),
            "profitAmount": getattr(backend_opp, 'expected_profit', 0.0),
            "profitPercent": getattr(backend_opp, 'price_spread', 0.0) * 100,
            "volume": getattr(backend_opp, 'amount_in', 0.0),
            "confidence": getattr(backend_opp, 'confidence_score', 0.0),
            "estimatedGas": 0.001,  # TODO: Get real gas estimate
            "netProfit": getattr(backend_opp, 'expected_profit', 0.0) - 0.001,
            "timeToExpire": getattr(backend_opp, 'execution_window_ms', 30000) // 1000,
            "riskLevel": "LOW" if getattr(backend_opp, 'confidence_score', 0.0) > 0.8 else "MEDIUM",
            "status": "ACTIVE",
            "createdAt": datetime.fromtimestamp(
                getattr(backend_opp, 'created_at', time.time())
            ).isoformat(),
            "updatedAt": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f"❌ Error mapping opportunity: {e}")
        return create_mock_opportunity()


def create_mock_opportunity() -> Dict[str, Any]:
    """Create a mock opportunity for testing"""
    return {
        "id": str(uuid.uuid4()),
        "pair": {
            "baseToken": {
                "address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                "symbol": "USDC",
                "name": "USD Coin",
                "decimals": 6
            },
            "quoteToken": {
                "address": "So11111111111111111111111111111111111111112",
                "symbol": "SOL",
                "name": "Solana", 
                "decimals": 9
            },
            "address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        },
        "buyExchange": "JUPITER",
        "sellExchange": "ORCA",
        "buyPrice": 0.0245,
        "sellPrice": 0.0252,
        "profitAmount": 0.0035,
        "profitPercent": 2.86,
        "volume": 0.5,
        "confidence": 0.92,
        "estimatedGas": 0.001,
        "netProfit": 0.0025,
        "timeToExpire": 25,
        "riskLevel": "LOW",
        "status": "ACTIVE",
        "createdAt": datetime.now(timezone.utc).isoformat(),
        "updatedAt": datetime.now(timezone.utc).isoformat()
    }


def map_backend_health_to_frontend(health_data: Dict[str, Any]) -> Dict[str, Any]:
    """Map backend health data to frontend schema"""
    providers = []
    dex_providers = health_data.get("dex_providers", {})
    
    for provider_name, provider_data in dex_providers.items():
        providers.append({
            "name": provider_name,
            "status": "HEALTHY" if provider_data.get("is_healthy", False) else "DOWN",
            "latency": provider_data.get("latency_ms", 0),
            "successRate": provider_data.get("success_rate", 0.0),
            "lastPing": datetime.now(timezone.utc).isoformat(),
            "uptime": provider_data.get("uptime_seconds", 3600)
        })
    
    # Map to exact frontend SystemHealth interface
    overall_health_score = health_data.get("overall_health_score", 0.8)
    overall_status = "HEALTHY" if overall_health_score > 0.7 else "DEGRADED" if overall_health_score > 0.3 else "DOWN"
    
    return {
        "overall": overall_status,  # Match frontend interface: 'HEALTHY' | 'DEGRADED' | 'DOWN'
        "providers": providers,
        "database": {
            "status": "CONNECTED",  # TODO: Get real database status
            "latency": 5  # TODO: Get real database latency
        },
        "websocket": {
            "status": "CONNECTED",  # TODO: Get real websocket status
            "connections": len(websocket_manager.active_connections) if websocket_manager else 0
        },
        "memoryUsage": 45.2,  # TODO: Get real memory usage percentage
        "cpuUsage": 23.8,     # TODO: Get real CPU usage percentage
        "lastUpdated": datetime.now(timezone.utc).isoformat()
    }


def create_mock_health() -> Dict[str, Any]:
    """Create mock health data for testing"""
    return {
        "overall": "HEALTHY",  # Match frontend interface: 'HEALTHY' | 'DEGRADED' | 'DOWN'
        "providers": [
            {"name": "JUPITER", "status": "HEALTHY", "latency": 45, "successRate": 0.98, "lastPing": datetime.now(timezone.utc).isoformat(), "uptime": 3600},
            {"name": "ORCA", "status": "HEALTHY", "latency": 52, "successRate": 0.96, "lastPing": datetime.now(timezone.utc).isoformat(), "uptime": 3600},
            {"name": "METEORA", "status": "HEALTHY", "latency": 38, "successRate": 0.99, "lastPing": datetime.now(timezone.utc).isoformat(), "uptime": 3600}
        ],
        "database": {
            "status": "CONNECTED",
            "latency": 5
        },
        "websocket": {
            "status": "CONNECTED",
            "connections": len(websocket_manager.active_connections) if websocket_manager else 0
        },
        "memoryUsage": 45.2,  # percentage
        "cpuUsage": 23.8,     # percentage
        "lastUpdated": datetime.now(timezone.utc).isoformat()
    }


# API Routes - Bridge to existing backend

@app.get("/api/arbitrage/opportunities")
async def get_opportunities():
    """Get arbitrage opportunities from existing backend"""
    try:
        logger.info("📊 Fetching arbitrage opportunities...")
        
        if arbitrage_finder:
            try:
                # Call existing sophisticated backend method
                backend_opportunities = await arbitrage_finder.find_opportunities()
                
                # Map backend schema to frontend schema
                frontend_opportunities = [
                    map_backend_opportunity_to_frontend(opp) 
                    for opp in backend_opportunities
                ]
                
                logger.info(f"✅ Found {len(frontend_opportunities)} opportunities")
                
            except Exception as e:
                logger.warning(f"⚠️ Backend scan failed, using mock data: {e}")
                frontend_opportunities = [create_mock_opportunity()]
        else:
            logger.info("📊 Using mock opportunities (backend not initialized)")
            frontend_opportunities = [create_mock_opportunity(), create_mock_opportunity()]
        
        # Broadcast new opportunities via WebSocket
        await websocket_manager.broadcast({
            "type": "opportunities_updated",
            "data": frontend_opportunities,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        return {
            "success": True,
            "data": {
                "data": frontend_opportunities,
                "pagination": {
                    "page": 1,
                    "limit": len(frontend_opportunities),
                    "total": len(frontend_opportunities),
                    "totalPages": 1,
                    "hasNext": False,
                    "hasPrev": False
                }
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get opportunities: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch opportunities: {str(e)}")


@app.get("/api/arbitrage/opportunities/{opportunity_id}")
async def get_opportunity(opportunity_id: str):
    """Get single arbitrage opportunity"""
    try:
        logger.info(f"📊 Fetching opportunity {opportunity_id}...")
        
        # For now, return a mock opportunity with the requested ID
        # TODO: Implement actual opportunity lookup from backend
        mock_opportunity = create_mock_opportunity()
        mock_opportunity["id"] = opportunity_id
        
        return {
            "success": True,
            "data": mock_opportunity
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get opportunity {opportunity_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch opportunity: {str(e)}")


@app.post("/api/arbitrage/opportunities/{opportunity_id}/execute")
async def execute_opportunity(opportunity_id: str):
    """Execute specific arbitrage opportunity"""
    try:
        logger.info(f"🚀 Executing arbitrage for opportunity {opportunity_id}")
        
        if opportunity_executor:
            try:
                # TODO: Map frontend request to backend execution parameters
                # execution_result = await opportunity_executor.execute_opportunity(execution_params)
                
                logger.info("📈 Opportunity execution initiated")
                
            except Exception as e:
                logger.warning(f"⚠️ Backend execution failed: {e}")
        
        # Mock successful execution for now
        execution_result = {
            "id": str(uuid.uuid4()),
            "opportunityId": opportunity_id,
            "status": "COMPLETED",
            "startTime": datetime.now(timezone.utc).isoformat(),
            "endTime": (datetime.now(timezone.utc)).isoformat(),
            "transactions": {
                "buy": {"hash": f"buy_tx_{uuid.uuid4().hex[:8]}", "status": "CONFIRMED"},
                "sell": {"hash": f"sell_tx_{uuid.uuid4().hex[:8]}", "status": "CONFIRMED"}
            },
            "actualProfit": 0.0035,
            "gasUsed": 0.001
        }
        
        # Broadcast execution update via WebSocket
        await websocket_manager.broadcast({
            "type": "trade_executed",
            "data": execution_result,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        logger.info(f"✅ Arbitrage execution completed: {execution_result['id']}")
        return {"success": True, "data": execution_result}
        
    except Exception as e:
        logger.error(f"❌ Failed to execute arbitrage: {e}")
        raise HTTPException(status_code=500, detail=f"Execution failed: {str(e)}")


@app.get("/api/arbitrage/executions/{execution_id}")
async def get_execution(execution_id: str):
    """Get execution status"""
    try:
        logger.info(f"📊 Fetching execution {execution_id}...")
        
        # Mock execution data
        execution_result = {
            "id": execution_id,
            "opportunityId": str(uuid.uuid4()),
            "status": "COMPLETED",
            "startTime": datetime.now(timezone.utc).isoformat(),
            "endTime": (datetime.now(timezone.utc)).isoformat(),
            "transactions": {
                "buy": {"hash": f"buy_tx_{uuid.uuid4().hex[:8]}", "status": "CONFIRMED"},
                "sell": {"hash": f"sell_tx_{uuid.uuid4().hex[:8]}", "status": "CONFIRMED"}
            },
            "actualProfit": 0.0035,
            "gasUsed": 0.001
        }
        
        return {"success": True, "data": execution_result}
        
    except Exception as e:
        logger.error(f"❌ Failed to get execution {execution_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch execution: {str(e)}")


@app.get("/api/arbitrage/history")
async def get_arbitrage_history():
    """Get arbitrage history - frontend compatibility endpoint"""
    try:
        logger.info("📊 Fetching arbitrage history...")
        
        # Mock arbitrage history for now
        mock_history = [
            {
                "id": str(uuid.uuid4()),
                "opportunityId": str(uuid.uuid4()),
                "status": "COMPLETED",
                "startTime": (datetime.now(timezone.utc) - timedelta(hours=1)).isoformat(),
                "endTime": (datetime.now(timezone.utc) - timedelta(hours=1, minutes=2)).isoformat(),
                "transactions": {
                    "buy": {"hash": f"buy_tx_{uuid.uuid4().hex[:8]}", "status": "CONFIRMED"},
                    "sell": {"hash": f"sell_tx_{uuid.uuid4().hex[:8]}", "status": "CONFIRMED"}
                },
                "actualProfit": 0.0035,
                "gasUsed": 0.001
            }
        ]
        
        return {
            "success": True,
            "data": {
                "data": mock_history,
                "pagination": {
                    "page": 1,
                    "limit": 50,
                    "total": len(mock_history),
                    "totalPages": 1,
                    "hasNext": False,
                    "hasPrev": False
                }
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get arbitrage history: {e}")
        raise HTTPException(status_code=500, detail=f"History fetch failed: {str(e)}")


@app.post("/api/arbitrage/scan")
async def trigger_scan():
    """Trigger manual arbitrage scan"""
    try:
        logger.info("🔍 Triggering manual arbitrage scan...")
        
        if arbitrage_finder:
            try:
                # Trigger scan in backend
                # await arbitrage_finder.trigger_manual_scan()
                logger.info("✅ Manual scan triggered successfully")
            except Exception as e:
                logger.warning(f"⚠️ Backend scan trigger failed: {e}")
        
        return {
            "success": True,
            "data": {"message": "Manual scan triggered successfully"}
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to trigger scan: {e}")
        raise HTTPException(status_code=500, detail=f"Scan trigger failed: {str(e)}")


@app.post("/api/arbitrage/execute")
async def execute_arbitrage(request: dict):
    """Execute arbitrage via existing OpportunityExecutor - legacy endpoint"""
    try:
        opportunity_id = request.get("opportunityId")
        if not opportunity_id:
            raise HTTPException(status_code=400, detail="Missing opportunityId")
        
        logger.info(f"🚀 Executing arbitrage for opportunity {opportunity_id}")
        
        if opportunity_executor:
            try:
                # TODO: Map frontend request to backend execution parameters
                # execution_result = await opportunity_executor.execute_opportunity(execution_params)
                
                logger.info("📈 Opportunity execution initiated")
                
            except Exception as e:
                logger.warning(f"⚠️ Backend execution failed: {e}")
        
        # Mock successful execution for now
        execution_result = {
            "id": str(uuid.uuid4()),
            "opportunityId": opportunity_id,
            "status": "COMPLETED",
            "startTime": datetime.now(timezone.utc).isoformat(),
            "endTime": (datetime.now(timezone.utc)).isoformat(),
            "transactions": {
                "buy": {"hash": f"buy_tx_{uuid.uuid4().hex[:8]}", "status": "CONFIRMED"},
                "sell": {"hash": f"sell_tx_{uuid.uuid4().hex[:8]}", "status": "CONFIRMED"}
            },
            "actualProfit": 0.0035,
            "gasUsed": 0.001
        }
        
        # Broadcast execution update via WebSocket
        await websocket_manager.broadcast({
            "type": "trade_executed",
            "data": execution_result,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        logger.info(f"✅ Arbitrage execution completed: {execution_result['id']}")
        return {"success": True, "data": execution_result}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to execute arbitrage: {e}")
        raise HTTPException(status_code=500, detail=f"Execution failed: {str(e)}")


@app.get("/api/system/health")
async def get_system_health():
    """Get system health from existing backend"""
    try:
        logger.info("🏥 Checking system health...")
        
        if logging_manager:
            try:
                # Call existing sophisticated health monitoring
                # AsyncTradingLogger doesn't have get_system_health, use mock for now
                frontend_health = create_mock_health()
                
            except Exception as e:
                logger.warning(f"⚠️ Backend health check failed: {e}")
                frontend_health = create_mock_health()
        else:
            logger.info("🏥 Using mock health data (backend not initialized)")
            frontend_health = create_mock_health()
        
        # Broadcast health update via WebSocket
        await websocket_manager.broadcast({
            "type": "health_update",
            "data": frontend_health,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        return {"success": True, "data": frontend_health}
        
    except Exception as e:
        logger.error(f"❌ Failed to get system health: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@app.get("/api/system/circuit-breaker")
async def get_circuit_breaker_status():
    """Get circuit breaker status from existing QuoteCircuitBreaker"""
    try:
        logger.info("🔌 Checking circuit breaker status...")
        
        if circuit_breaker:
            try:
                # Get circuit breaker status from existing system
                cb_status = circuit_breaker.get_all_status()
                
                frontend_cb = {
                    "isActive": any(
                        status.get("circuit_state") == "open" 
                        for status in cb_status.values()
                    ),
                    "triggeredRules": [],  # TODO: Map from cb_status
                    "dailyLoss": 0.0,      # TODO: Get from trading stats
                    "dailyLossLimit": 1.0,
                    "positionSize": 0.05,
                    "positionSizeLimit": 0.1,
                    "errorRate": 0.0,
                    "errorRateLimit": 0.1,
                    "lastReset": datetime.now(timezone.utc).isoformat()
                }
                
            except Exception as e:
                logger.warning(f"⚠️ Circuit breaker check failed: {e}")
                frontend_cb = {
                    "isActive": False,
                    "triggeredRules": [],
                    "dailyLoss": 0.0,
                    "dailyLossLimit": 1.0,
                    "positionSize": 0.05,
                    "positionSizeLimit": 0.1,
                    "errorRate": 0.0,
                    "errorRateLimit": 0.1,
                    "lastReset": datetime.now(timezone.utc).isoformat()
                }
        else:
            frontend_cb = {
                "isActive": False,
                "triggeredRules": [],
                "dailyLoss": 0.0,
                "dailyLossLimit": 1.0,
                "positionSize": 0.05,
                "positionSizeLimit": 0.1,
                "errorRate": 0.0,
                "errorRateLimit": 0.1,
                "lastReset": datetime.now(timezone.utc).isoformat()
            }
        
        return {"success": True, "data": frontend_cb}
        
    except Exception as e:
        logger.error(f"❌ Failed to get circuit breaker status: {e}")
        raise HTTPException(status_code=500, detail=f"Circuit breaker check failed: {str(e)}")


@app.post("/api/system/circuit-breaker/reset")
async def reset_circuit_breaker():
    """Reset circuit breaker"""
    try:
        logger.info("🔄 Resetting circuit breaker...")
        
        if circuit_breaker:
            try:
                # Reset circuit breaker in backend
                # circuit_breaker.reset_all()
                logger.info("✅ Circuit breaker reset successfully")
            except Exception as e:
                logger.warning(f"⚠️ Backend circuit breaker reset failed: {e}")
        
        return {
            "success": True,
            "data": {"message": "Circuit breaker reset successfully"}
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to reset circuit breaker: {e}")
        raise HTTPException(status_code=500, detail=f"Circuit breaker reset failed: {str(e)}")


@app.get("/api/system/config")
async def get_system_config():
    """Get trading configuration"""
    try:
        logger.info("⚙️ Fetching system configuration...")
        
        # Mock configuration for now
        mock_config = {
            "maxPositionSize": 1.0,
            "minProfitThreshold": 0.001,
            "maxSlippage": 0.005,
            "gasPrice": 0.001,
            "enableAutoTrading": True,
            "riskTolerance": "MODERATE"
        }
        
        return {"success": True, "data": mock_config}
        
    except Exception as e:
        logger.error(f"❌ Failed to get system config: {e}")
        raise HTTPException(status_code=500, detail=f"Config fetch failed: {str(e)}")


@app.put("/api/system/config")
async def update_system_config(config: dict):
    """Update trading configuration"""
    try:
        logger.info("⚙️ Updating system configuration...")
        
        # TODO: Implement actual config update in backend
        logger.info(f"✅ Configuration updated: {config}")
        
        return {
            "success": True,
            "data": config
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to update system config: {e}")
        raise HTTPException(status_code=500, detail=f"Config update failed: {str(e)}")


@app.get("/api/trading/history")
async def get_trading_history():
    """Get trading history - TODO: Implement with database"""
    try:
        logger.info("📊 Fetching trading history...")
        
        # Mock trading history for now
        mock_history = [
            {
                "id": str(uuid.uuid4()),
                "opportunityId": str(uuid.uuid4()),
                "timestamp": (datetime.now(timezone.utc) - timedelta(hours=1)).isoformat(),
                "pair": "USDC/SOL",
                "buyExchange": "JUPITER",
                "sellExchange": "ORCA",
                "profitAmount": 0.0035,
                "profitPercent": 2.86,
                "status": "COMPLETED",
                "gasUsed": 0.001
            }
        ]
        
        return {
            "success": True,
            "data": {
                "data": mock_history,
                "pagination": {
                    "page": 1,
                    "limit": 50,
                    "total": len(mock_history),
                    "totalPages": 1,
                    "hasNext": False,
                    "hasPrev": False
                }
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get trading history: {e}")
        raise HTTPException(status_code=500, detail=f"History fetch failed: {str(e)}")


@app.get("/api/trading/metrics")
async def get_trading_metrics():
    """Get trading metrics - TODO: Implement with performance aggregation"""
    try:
        logger.info("📈 Fetching trading metrics...")
        
        # Mock metrics for now
        mock_metrics = {
            "totalTrades": 127,
            "totalProfit": 0.425,
            "winRate": 0.94,
            "avgProfit": 0.0033,
            "bestTrade": 0.0087,
            "worstTrade": -0.0012
        }
        
        return {"success": True, "data": mock_metrics}
        
    except Exception as e:
        logger.error(f"❌ Failed to get trading metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Metrics fetch failed: {str(e)}")


@app.get("/api/system/stats")
async def get_system_stats(timeframe: str = "24h"):
    """Get system statistics - frontend compatibility endpoint"""
    try:
        logger.info(f"📊 Fetching system stats for timeframe: {timeframe}")
        
        # Mock system stats compatible with frontend expectations
        mock_stats = {
            "totalTrades": 127,
            "totalProfit": 0.425,
            "winRate": 0.94,
            "avgProfit": 0.0033,
            "bestTrade": 0.0087,
            "worstTrade": -0.0012,
            "totalVolume": 12547.89,
            "avgExecutionTime": 2840,
            "successRate": 0.94,
            "totalGasUsed": 0.127,
            "avgGasPerTrade": 0.001,
            "profitFactor": 4.39,
            "sharpeRatio": 1.67,
            "maxDrawdown": -0.045,
            "timeframe": timeframe,
            "lastUpdated": datetime.now(timezone.utc).isoformat()
        }
        
        return {"success": True, "data": mock_stats}
        
    except Exception as e:
        logger.error(f"❌ Failed to get system stats: {e}")
        raise HTTPException(status_code=500, detail=f"System stats fetch failed: {str(e)}")


@app.get("/api/trading/providers")
async def get_provider_performance():
    """Get DEX provider performance statistics"""
    try:
        # Mock provider performance data for now
        providers = [
            {
                "name": "Jupiter",
                "success_rate": 0.98,
                "avg_response_time": 120,
                "total_quotes": 15420,
                "status": "healthy"
            },
            {
                "name": "Orca",
                "success_rate": 0.95,
                "avg_response_time": 180,
                "total_quotes": 8920,
                "status": "healthy"
            },
            {
                "name": "Meteora",
                "success_rate": 0.92,
                "avg_response_time": 200,
                "total_quotes": 5670,
                "status": "degraded"
            }
        ]
        
        return {
            "success": True,
            "data": providers,
            "message": "Provider performance retrieved successfully"
        }
    except Exception as e:
        logger.error(f"❌ Failed to get provider performance: {e}")
        raise HTTPException(status_code=500, detail="Failed to get provider performance")

@app.get("/api/trading/volume")
async def get_trading_volume():
    """Get current trading volume data"""
    try:
        # Mock volume data for now
        import random
        
        volume_data = {
            "totalVolume": random.uniform(1000000, 5000000),  # Random volume between 1M-5M
            "token": "SOL",
            "exchange": "all",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "volumeChange": random.uniform(-15, 15),  # Random change percentage
            "volume24h": random.uniform(5000000, 20000000),  # 24h volume
            "volume7d": random.uniform(30000000, 100000000),  # 7d volume
        }
        
        return {
            "success": True,
            "data": volume_data,
            "message": "Volume data retrieved successfully"
        }
    except Exception as e:
        logger.error(f"❌ Failed to get trading volume: {e}")
        raise HTTPException(status_code=500, detail="Failed to get trading volume")

# === AUTHENTICATION ENDPOINTS ===

# Simple in-memory user store (replace with database in production)
users_db = {}

@app.post("/api/auth/signup")
async def signup(request: SignupRequest):
    """Register a new user"""
    try:
        # Validate email format
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, request.email):
            return LoginResponse(
                success=False,
                data={},
                message="Please enter a valid email address"
            )
        
        # Validate password strength
        if len(request.password) < 6:
            return LoginResponse(
                success=False,
                data={},
                message="Password must be at least 6 characters long"
            )
        
        # Validate name
        if not request.name or len(request.name.strip()) < 2:
            return LoginResponse(
                success=False,
                data={},
                message="Name must be at least 2 characters long"
            )
        
        # Check if user already exists
        if request.email in users_db:
            return LoginResponse(
                success=False,
                data={},
                message="An account with this email already exists. Please sign in instead."
            )
        
        # Create new user
        user_id = f"user_{len(users_db) + 1}"
        user_data = {
            "id": user_id,
            "email": request.email,
            "role": "trader",  # Default role for new users
            "name": request.name.strip()
        }
        
        # Store user (in production, hash the password)
        users_db[request.email] = {
            "user": user_data,
            "password": request.password  # In production, hash this!
        }
        
        # Generate token
        token = f"mock_jwt_token_{request.email}_{int(time.time())}"
        
        logger.info(f"✅ New user {request.email} signed up successfully")
        
        return LoginResponse(
            success=True,
            data={
                "token": token,
                "user": user_data
            },
            message="Account created successfully! You can now sign in."
        )
        
    except Exception as e:
        logger.error(f"❌ Signup failed: {e}")
        return LoginResponse(
            success=False,
            data={},
            message="Unable to create account. Please try again or contact support if the problem persists."
        )

@app.post("/api/auth/login")
async def login(request: LoginRequest):
    """Authenticate user and return JWT token"""
    try:
        # Validate email format
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, request.email):
            return LoginResponse(
                success=False,
                data={},
                message="Please enter a valid email address"
            )
        
        # Validate password
        if not request.password:
            return LoginResponse(
                success=False,
                data={},
                message="Password is required"
            )
        
        # Check if user exists and password matches
        if request.email not in users_db:
            return LoginResponse(
                success=False,
                data={},
                message="No account found with this email address. Please check your email or sign up for a new account."
            )
        
        stored_user = users_db[request.email]
        if stored_user["password"] != request.password:
            return LoginResponse(
                success=False,
                data={},
                message="Incorrect password. Please check your password and try again."
            )
        
        # Get user data
        user_data = stored_user["user"]
        
        # Generate token
        token = f"mock_jwt_token_{request.email}_{int(time.time())}"
        
        logger.info(f"✅ User {request.email} logged in successfully")
        
        return LoginResponse(
            success=True,
            data={
                "token": token,
                "user": user_data
            },
            message="Login successful"
        )
        
    except Exception as e:
        logger.error(f"❌ Login failed: {e}")
        return LoginResponse(
            success=False,
            data={},
            message="Login failed. Please try again or contact support if the problem persists."
        )

@app.post("/api/auth/logout")
async def logout():
    """Logout user and invalidate token"""
    try:
        # In production, this would invalidate the JWT token
        logger.info("✅ User logged out successfully")
        
        return {
            "success": True,
            "data": {},
            "message": "Logout successful"
        }
        
    except Exception as e:
        logger.error(f"❌ Logout failed: {e}")
        raise HTTPException(status_code=500, detail="Logout failed")

@app.post("/api/auth/refresh")
async def refresh_token():
    """Refresh JWT token"""
    try:
        # Mock token refresh - replace with real JWT refresh logic
        new_token = f"mock_jwt_token_refreshed_{int(time.time())}"
        
        return {
            "success": True,
            "data": {
                "token": new_token
            },
            "message": "Token refreshed successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ Token refresh failed: {e}")
        raise HTTPException(status_code=500, detail="Token refresh failed")

# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket.accept()  # Accept the WebSocket connection first
    await websocket_manager.connect(websocket)
    
    try:
        # Send initial system status
        try:
            health_data = create_mock_health()  # Use mock for now
            await websocket_manager.send_to_connection(websocket, {
                "type": "health_update",
                "data": health_data,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        except Exception as e:
            logger.warning(f"⚠️ Failed to send initial health status: {e}")
        
        # Keep connection alive and handle client messages
        while True:
            try:
                # Wait for client messages with timeout
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                
                # Use enhanced message handler
                await websocket_manager.handle_client_message(websocket, data)
                    
            except asyncio.TimeoutError:
                # Send heartbeat
                await websocket_manager.send_heartbeat()
                
    except WebSocketDisconnect:
        logger.info("🔌 WebSocket disconnected normally")
        await websocket_manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"❌ WebSocket error: {e}")
        await websocket_manager.disconnect(websocket)


# Health check endpoint
@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": "1.0.0",
        "backend_engines": {
            "arbitrage_finder": arbitrage_finder is not None,
            "opportunity_executor": opportunity_executor is not None,
            "logging_manager": logging_manager is not None,
            "circuit_breaker": circuit_breaker is not None
        },
        "websocket_stats": websocket_manager.get_connection_stats(),
        "api_endpoints": {
            "opportunities": "/api/arbitrage/opportunities",
            "execute": "/api/arbitrage/execute",
            "health": "/api/system/health",
            "circuit_breaker": "/api/system/circuit-breaker",
            "stats": "/api/system/stats",
            "trading_history": "/api/trading/history",
            "trading_metrics": "/api/trading/metrics",
            "providers": "/api/trading/providers"
        }
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "PRISM DEX Arbitrage API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "websocket": "/ws"
    }


if __name__ == "__main__":
    import uvicorn
    
    logger.info("🚀 Starting FastAPI bridge server...")
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8001,
        log_level="info",
        reload=True
    ) 