"""
Configuration management for FastAPI Bridge Server
"""

import os


class Config:
    """Configuration class for API server settings."""

    # Server Configuration
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    # Port 8001 - SurrealDB occupies 8000
    API_PORT: int = int(os.getenv("API_PORT", "8001"))

    # Debug Mode
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"

    # Solana Configuration
    SOLANA_RPC_URL: str = os.getenv(
        "SOLANA_RPC_URL", "https://api.mainnet-beta.solana.com"
    )

    # DEX Provider URLs
    JUPITER_API_URL: str = os.getenv("JUPITER_API_URL", "https://quote-api.jup.ag/v6")

    METEORA_SDK_ENABLED: bool = (
        os.getenv("METEORA_SDK_ENABLED", "true").lower() == "true"
    )

    # WebSocket Configuration
    WS_HEARTBEAT_INTERVAL: int = int(os.getenv("WS_HEARTBEAT_INTERVAL", "30"))
    WS_MAX_CONNECTIONS: int = int(os.getenv("WS_MAX_CONNECTIONS", "100"))

    # Circuit Breaker Configuration
    CIRCUIT_BREAKER_ERROR_RATE_LIMIT: float = float(
        os.getenv("CIRCUIT_BREAKER_ERROR_RATE_LIMIT", "0.1")
    )

    # Performance Configuration
    ASYNC_WORKER_POOL_SIZE: int = int(os.getenv("ASYNC_WORKER_POOL_SIZE", "10"))
    REQUEST_TIMEOUT_SECONDS: int = int(os.getenv("REQUEST_TIMEOUT_SECONDS", "30"))


config = Config()


# Environment variable instructions for setup
ENV_INSTRUCTIONS = """
# Create .env file in api_server/ directory with these variables:

API_HOST=0.0.0.0
API_PORT=8001
DEBUG=true
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
WALLET_PRIVATE_KEY=your_private_key_here
JUPITER_API_URL=https://quote-api.jup.ag/v6
ORCA_API_URL=https://api.orca.so/v1
METEORA_SDK_ENABLED=true
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100
LOG_LEVEL=INFO
MAX_POSITION_SIZE=0.1
DAILY_LOSS_LIMIT=1.0
CIRCUIT_BREAKER_ERROR_RATE_LIMIT=0.1
ASYNC_WORKER_POOL_SIZE=10
REQUEST_TIMEOUT_SECONDS=30
"""
