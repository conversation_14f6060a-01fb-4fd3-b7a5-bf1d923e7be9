"""
WebSocket Manager for Real-time Updates

Handles WebSocket connections, broadcasting, and real-time communication
between FastAPI backend and React frontend.
"""

import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List

from fastapi import WebSocket


class WebSocketManager:
    """Enhanced WebSocket manager for real-time trading updates."""

    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
        self.subscriptions: Dict[str, List[WebSocket]] = {}
        self.logger = logging.getLogger(__name__)

    async def connect(self, websocket: WebSocket):
        """Accept and register a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_metadata[websocket] = {
            "subscriptions": [],
            "last_ping": datetime.now(timezone.utc),
            "connected_at": datetime.now(timezone.utc),
        }

        self.logger.info(
            f"🔌 WebSocket connected (total: {len(self.active_connections)})"
        )

    async def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection and clean up metadata."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

        if websocket in self.connection_metadata:
            # Remove from all subscriptions
            for event_type in self.connection_metadata[websocket]["subscriptions"]:
                if event_type in self.subscriptions:
                    if websocket in self.subscriptions[event_type]:
                        self.subscriptions[event_type].remove(websocket)

            del self.connection_metadata[websocket]

    async def subscribe(self, websocket: WebSocket, event_type: str):
        """Subscribe a connection to specific event types."""
        if event_type not in self.subscriptions:
            self.subscriptions[event_type] = []

        if websocket not in self.subscriptions[event_type]:
            self.subscriptions[event_type].append(websocket)
            self.connection_metadata[websocket]["subscriptions"].append(event_type)

    async def unsubscribe(self, websocket: WebSocket, event_type: str):
        """Unsubscribe a connection from specific event types."""
        if event_type in self.subscriptions:
            if event_type in self.connection_metadata[websocket]["subscriptions"]:
                self.connection_metadata[websocket]["subscriptions"].remove(event_type)

            if websocket in self.subscriptions[event_type]:
                self.subscriptions[event_type].remove(websocket)

    async def broadcast_opportunity_discovered(self, opportunity: Dict[str, Any]):
        """Broadcast new arbitrage opportunity to all subscribers."""
        message = {
            "type": "opportunity_discovered",
            "data": opportunity,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        await self._broadcast_to_subscribers("opportunity_discovered", message)
        self.logger.debug(
            f"📢 Broadcasted opportunity discovery: "
            f"{opportunity.get('id', 'unknown')}"
        )

    async def broadcast_opportunity_updated(self, opportunity: Dict[str, Any]):
        """Broadcast opportunity updates to all subscribers."""
        message = {
            "type": "opportunity_updated",
            "data": opportunity,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        await self._broadcast_to_subscribers("opportunity_updated", message)
        self.logger.debug(
            f"📢 Broadcasted opportunity update: " f"{opportunity.get('id', 'unknown')}"
        )

    async def broadcast_circuit_breaker_triggered(self, breaker_status: Dict[str, Any]):
        """Broadcast circuit breaker state changes."""
        message = {
            "type": "circuit_breaker_triggered",
            "data": breaker_status,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        await self._broadcast_to_subscribers("circuit_breaker_triggered", message)
        self.logger.info(
            f"🚨 Broadcasted circuit breaker: "
            f"{breaker_status.get('provider', 'unknown')} -> "
            f"{breaker_status.get('state', 'unknown')}"
        )

    async def broadcast_system_health_update(self, health_data: Dict[str, Any]):
        """Broadcast system health changes."""
        message = {
            "type": "system_health_update",
            "data": health_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        await self._broadcast_to_subscribers("system_health_update", message)
        self.logger.debug("💚 Broadcasted system health update")

    async def broadcast_trading_status_change(self, status_data: Dict[str, Any]):
        """Broadcast trading status changes to all subscribers."""
        message = {
            "type": "trading_status_change",
            "data": status_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        await self._broadcast_to_subscribers("trading_status_change", message)
        self.logger.debug("📊 Broadcasted trading status change")

    async def _broadcast_to_subscribers(self, event_type: str, message: Dict[str, Any]):
        """Internal method to broadcast messages to event subscribers."""
        if event_type not in self.subscriptions:
            return

        # Avoid modification during iteration
        subscribers = self.subscriptions[event_type].copy()

        for websocket in subscribers:
            try:
                await self.send_to_connection(websocket, message)
            except Exception as e:
                self.logger.warning(f"Failed to send to subscriber: {e}")
                # Remove dead connection
                await self.disconnect(websocket)

    async def send_to_connection(self, websocket: WebSocket, message: Dict[str, Any]):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            self.logger.error(f"Failed to send message to connection: {e}")
            await self.disconnect(websocket)

    async def broadcast(self, message: Dict[str, Any], subscription_filter: str = None):
        """Broadcast message to all connections or filtered subscribers."""
        if subscription_filter:
            await self._broadcast_to_subscribers(subscription_filter, message)
        else:
            # Broadcast to all active connections
            dead_connections = []

            for websocket in self.active_connections:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    self.logger.error(f"Failed to broadcast to connection: {e}")
                    dead_connections.append(websocket)

            # Clean up dead connections
            for dead_websocket in dead_connections:
                await self.disconnect(dead_websocket)

    async def handle_client_message(self, websocket: WebSocket, data: str):
        """Handle incoming client messages."""
        try:
            message = json.loads(data)
            message_type = message.get("type")

            if message_type == "ping":
                # Update last ping time
                self.connection_metadata[websocket]["last_ping"] = datetime.now(
                    timezone.utc
                )
                await self.send_to_connection(websocket, {"type": "pong"})

            elif message_type == "subscribe":
                event_type = message.get("event_type")
                if event_type:
                    await self.subscribe(websocket, event_type)
                    await self.send_to_connection(
                        websocket, {"type": "subscribed", "event_type": event_type}
                    )

            elif message_type == "unsubscribe":
                event_type = message.get("event_type")
                if event_type:
                    await self.unsubscribe(websocket, event_type)
                    await self.send_to_connection(
                        websocket, {"type": "unsubscribed", "event_type": event_type}
                    )

            elif message_type == "echo":
                # Simple echo for testing
                await self.send_to_connection(
                    websocket, {"type": "echo", "data": message.get("data", "")}
                )

        except json.JSONDecodeError:
            await self.send_to_connection(
                websocket, {"type": "error", "message": "Invalid JSON format"}
            )
        except Exception as e:
            self.logger.error(f"Error handling client message: {e}")
            await self.send_to_connection(
                websocket, {"type": "error", "message": "Internal server error"}
            )

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get statistics about active connections."""
        connection_ages = []
        for metadata in self.connection_metadata.values():
            age = (
                datetime.now(timezone.utc) - metadata["connected_at"]
            ).total_seconds()
            connection_ages.append(age)

        return {
            "total_connections": len(self.active_connections),
            "total_subscriptions": sum(
                len(subs) for subs in self.subscriptions.values()
            ),
            "avg_connection_age_seconds": (
                sum(connection_ages) / len(connection_ages) if connection_ages else 0
            ),
            "subscription_types": list(self.subscriptions.keys()),
        }


# Global WebSocket manager instance
websocket_manager = WebSocketManager()
