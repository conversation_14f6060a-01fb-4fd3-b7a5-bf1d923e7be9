import * as React from "react"
import { cn } from "@/lib/utils"
import { Button, type ButtonProps } from "./button"

// === Loading Spinner Component ===

const LoadingSpinner = ({ className }: { className?: string }) => (
  <svg
    className={cn("animate-spin", className)}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="currentColor"
      d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
)

// === Enhanced Button Component ===

export interface EnhancedButtonProps extends ButtonProps {
  loading?: boolean
  loadingText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

export const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  (
    {
      className,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading
    
    return (
      <Button
        ref={ref}
        className={cn(
          "inline-flex items-center justify-center gap-2",
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        {/* Left Icon or Loading Spinner */}
        {loading ? (
          <LoadingSpinner className="w-4 h-4" />
        ) : leftIcon ? (
          <span className="flex items-center">{leftIcon}</span>
        ) : null}
        
        {/* Button Content */}
        {loading && loadingText ? loadingText : children}
        
        {/* Right Icon */}
        {rightIcon && !loading && (
          <span className="flex items-center">{rightIcon}</span>
        )}
      </Button>
    )
  }
)
EnhancedButton.displayName = "EnhancedButton"

// === Mobile-Specific Button Components ===

// Mobile full-width button
export const MobileFullButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => (
    <Button
      ref={ref}
      className={cn("h-12 px-4 py-3 text-base w-full", props.className)}
      {...props}
    />
  )
)
MobileFullButton.displayName = "MobileFullButton"

// Mobile touch-friendly button
export const MobileTouchButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => (
    <Button
      ref={ref}
      className={cn("h-12 px-6 py-3 text-base", props.className)}
      {...props}
    />
  )
)
MobileTouchButton.displayName = "MobileTouchButton"

// Mobile icon button
export const MobileIconButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => (
    <Button
      ref={ref}
      size="icon"
      variant="ghost"
      className={cn("h-12 w-12", props.className)}
      {...props}
    />
  )
)
MobileIconButton.displayName = "MobileIconButton" 