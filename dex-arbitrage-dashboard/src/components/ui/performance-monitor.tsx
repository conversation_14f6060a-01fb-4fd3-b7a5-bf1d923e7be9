import React, { useEffect, useRef } from 'react';

interface PerformanceMonitorProps {
  componentName: string;
  children: React.ReactNode;
  threshold?: number;
}

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage?: number;
  timestamp: number;
}

export function PerformanceMonitor({ 
  componentName, 
  children, 
  threshold = 16 
}: PerformanceMonitorProps) {
  const startTime = useRef<number>(0);
  const metrics = useRef<PerformanceMetrics[]>([]);

  useEffect(() => {
    startTime.current = performance.now();
    
    return () => {
      const renderTime = performance.now() - startTime.current;
      
      // Store metrics
      const metric: PerformanceMetrics = {
        renderTime,
        timestamp: Date.now(),
      };
      
      metrics.current.push(metric);
      
      // Keep only last 10 metrics
      if (metrics.current.length > 10) {
        metrics.current = metrics.current.slice(-10);
      }
      
      // Log slow renders
      if (renderTime > threshold) {
        console.warn(
          `🚨 Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms (threshold: ${threshold}ms)`
        );
      }
      
      // Log performance metrics in development
      if (process.env.NODE_ENV === 'development') {
        console.log(
          `📊 ${componentName} render: ${renderTime.toFixed(2)}ms`
        );
      }
    };
  }, [componentName, threshold]);

  return <>{children}</>;
} 