import { cn } from '@/lib/utils';

interface NetworkStatusProps {
  className?: string;
}

export function NetworkStatus({ className }: NetworkStatusProps) {
  const isOnline = navigator.onLine;

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      <div className={cn(
        "w-2 h-2 rounded-full",
        isOnline ? "bg-green-500" : "bg-red-500"
      )} />
      <span className="text-xs">
        {isOnline ? 'ONLINE' : 'OFFLINE'}
      </span>
    </div>
  );
} 