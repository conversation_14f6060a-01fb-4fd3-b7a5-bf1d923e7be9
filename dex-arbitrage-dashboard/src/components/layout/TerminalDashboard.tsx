import { useEffect } from 'react';
import { useWebSocketConnection } from '@/hooks/useArbitrage';
import { useResponsive } from '@/hooks/useResponsive';
import { Routes, Route } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { useTheme } from '@/contexts/theme-hooks';
import { Toaster } from '@/components/ui/sonner';
import { TerminalNavigation } from './TerminalNavigation';
import { TerminalOpportunitiesTable } from './TerminalOpportunitiesTable';
import { TerminalSettings } from './TerminalSettings';
import TradingHistory from '@/components/features/TradingHistory';

// === ASCII Art ===
const ASCII_BANNER = `
╔══════════════════════════════════════════════════════════════════════════════╗
║  ██████╗ ███████╗██╗  ██╗    ██████╗  █████╗ ███████╗██╗  ██╗██████╗  ██████╗ ║
║  ██╔══██╗██╔════╝╚██╗██╔╝    ██╔══██╗██╔══██╗██╔════╝██║  ██║██╔══██╗██╔═══██╗║
║  ██║  ██║█████╗   ╚███╔╝     ██║  ██║███████║███████╗███████║██████╔╝██║   ██║║
║  ██║  ██║██╔══╝   ██╔██╗     ██║  ██║██╔══██║╚════██║██╔══██║██╔══██╗██║   ██║║
║  ██████╔╝███████╗██╔╝ ██╗    ██████╔╝██║  ██║███████║██║  ██║██████╔╝╚██████╔╝║
║  ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚═════╝ ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═════╝  ╚═════╝ ║
║                           ARBITRAGE TRADING TERMINAL                          ║
╚══════════════════════════════════════════════════════════════════════════════╝
`;

export function TerminalDashboard() {
  const queryClient = useQueryClient();
  const { isMobile } = useResponsive();
  const { currentTheme } = useTheme();
  const { connectionState, connectionHealth } = useWebSocketConnection();

  // Initialize WebSocket connection and setup query integration
  useEffect(() => {
    try {
      // Setup WebSocket integration with React Query
      // Note: wsQueryHelpers would be imported from websocket.ts
      // wsQueryHelpers.setupQueryInvalidation(queryClient);
      // wsQueryHelpers.setupOptimisticUpdates(queryClient);
      
      // Initialize WebSocket connection with error handling
      // initializeWebSocket();
    } catch (error) {
      console.warn('WebSocket initialization failed, continuing without real-time updates:', error);
      // Don't let WebSocket failures block the app
    }
  }, [queryClient]);

  if (isMobile) {
    return null; // Mobile uses different layout
  }

  return (
    <div className="min-h-screen bg-black text-green-400 font-mono">
      <Toaster />
      
      {/* Terminal Header */}
      <div className="border-b border-green-600 p-4">
        <pre className="ascii-header text-center text-green-400">
          {ASCII_BANNER}
        </pre>
        
        {/* System Status Bar */}
        <div className="flex justify-between items-center mt-4 text-sm">
          <div className="flex space-x-4">
            <span className="ascii-dim">Status: {connectionState}</span>
            <span className="ascii-dim">Theme: {currentTheme}</span>
            <span className="ascii-dim">Time: {new Date().toLocaleTimeString()}</span>
          </div>
          
          <div className="flex space-x-4">
            <span className="ascii-dim">Messages: {connectionHealth?.messageCount || 0}</span>
            <span className="ascii-dim">Errors: {connectionHealth?.errorCount || 0}</span>
            <span className="ascii-dim">Uptime: {new Date().toLocaleDateString()}</span>
          </div>
        </div>
      </div>

      {/* Terminal Navigation */}
      <TerminalNavigation />

      {/* Main Content */}
      <div className="p-4">
        <Routes>
          <Route path="/" element={<TerminalOpportunitiesTable />} />
          <Route path="/history" element={<TradingHistory />} />
          <Route path="/settings" element={<TerminalSettings />} />
        </Routes>
      </div>

      {/* Terminal prompt */}
      <div className="p-4 border-t border-gray-700">
        <div className="ascii-box">
          <pre className="ascii-dim">
{`admin@dex-arbitrage:~$ █`}
          </pre>
        </div>
      </div>
    </div>
  );
} 