import React from 'react';
import { useAuth } from '@/contexts/auth-hooks';
import { useTheme } from '@/contexts/theme-hooks';
import { TERMINAL_THEMES } from '@/contexts/theme-constants';

export function MobileHeader() {
  const { user } = useAuth();
  const { currentTheme, setTheme } = useTheme();

  const toggleTheme = () => {
    // Cycle through themes: classic -> amber -> blue -> matrix -> white -> purple -> cyan
    const themes: Array<keyof typeof TERMINAL_THEMES> = [
      'classic', 'amber', 'blue', 'matrix', 'white', 'purple', 'cyan'
    ];
    const currentIndex = themes.indexOf(currentTheme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-4 py-3">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">D</span>
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">DEX Arbitrage</h1>
            <p className="text-xs text-gray-500">Trading Dashboard</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
            title={`Current theme: ${currentTheme}`}
          >
            {currentTheme === 'white' ? '☀️' : '🌙'}
          </button>
          
          {/* User Menu */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {user?.name?.charAt(0) || 'U'}
              </span>
            </div>
            <div className="hidden sm:block">
              <p className="text-sm font-medium text-gray-900">{user?.name || 'User'}</p>
              <p className="text-xs text-gray-500">{user?.role || 'viewer'}</p>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
} 