import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { createAuthenticApiClient } from '@/lib/authenticApi';
import type { ArbitrageOpportunity } from '@/types/api';

const apiClient = createAuthenticApiClient();

export function TerminalOpportunitiesTable() {
  const [sortField, setSortField] = useState<keyof ArbitrageOpportunity>('profitPercent');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const { data: opportunitiesResponse, isLoading, error } = useQuery({
    queryKey: ['opportunities'],
    queryFn: () => apiClient.getOpportunities(),
    refetchInterval: 5000, // Refresh every 5 seconds
  });

  const opportunities = useMemo(() => {
    if (!opportunitiesResponse?.data?.data) return [];
    
    return [...opportunitiesResponse.data.data].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue) 
          : bValue.localeCompare(aValue);
      }
      
      return 0;
    });
  }, [opportunitiesResponse?.data?.data, sortField, sortDirection]);

  const handleSort = (field: keyof ArbitrageOpportunity) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const getSortDirection = (field: keyof ArbitrageOpportunity) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="text-green-400">Loading opportunities...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-400">Error loading opportunities: {error.message}</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-green-400">Arbitrage Opportunities</h2>
        <div className="text-sm text-green-500">
          {opportunities.length} opportunities found
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border border-green-600">
          <thead>
            <tr className="bg-green-600 text-black">
              <th 
                className="px-4 py-2 text-left cursor-pointer hover:bg-green-500"
                onClick={() => handleSort('pair')}
              >
                Pair {getSortDirection('pair')}
              </th>
              <th 
                className="px-4 py-2 text-left cursor-pointer hover:bg-green-500"
                onClick={() => handleSort('buyPrice')}
              >
                Buy Price {getSortDirection('buyPrice')}
              </th>
              <th 
                className="px-4 py-2 text-left cursor-pointer hover:bg-green-500"
                onClick={() => handleSort('sellPrice')}
              >
                Sell Price {getSortDirection('sellPrice')}
              </th>
              <th 
                className="px-4 py-2 text-left cursor-pointer hover:bg-green-500"
                onClick={() => handleSort('profitPercent')}
              >
                Profit % {getSortDirection('profitPercent')}
              </th>
              <th 
                className="px-4 py-2 text-left cursor-pointer hover:bg-green-500"
                onClick={() => handleSort('volume')}
              >
                Volume {getSortDirection('volume')}
              </th>
              <th 
                className="px-4 py-2 text-left cursor-pointer hover:bg-green-500"
                onClick={() => handleSort('status')}
              >
                Status {getSortDirection('status')}
              </th>
            </tr>
          </thead>
          <tbody>
            {opportunities.map((opportunity) => (
              <tr key={opportunity.id} className="border-t border-green-600 hover:bg-green-600 hover:bg-opacity-10">
                <td className="px-4 py-2 text-green-400">
                  {opportunity.pair?.baseToken?.symbol}/{opportunity.pair?.quoteToken?.symbol}
                </td>
                <td className="px-4 py-2 text-green-400">
                  ${opportunity.buyPrice?.toFixed(6)}
                </td>
                <td className="px-4 py-2 text-green-400">
                  ${opportunity.sellPrice?.toFixed(6)}
                </td>
                <td className="px-4 py-2 text-green-400">
                  {opportunity.profitPercent?.toFixed(2)}%
                </td>
                <td className="px-4 py-2 text-green-400">
                  {opportunity.volume?.toFixed(4)}
                </td>
                <td className="px-4 py-2">
                  <span className={cn(
                    'px-2 py-1 text-xs rounded',
                    opportunity.status === 'ACTIVE' && 'bg-green-600 text-black',
                    opportunity.status === 'EXPIRED' && 'bg-red-600 text-white',
                    opportunity.status === 'EXECUTING' && 'bg-yellow-600 text-black'
                  )}>
                    {opportunity.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
} 