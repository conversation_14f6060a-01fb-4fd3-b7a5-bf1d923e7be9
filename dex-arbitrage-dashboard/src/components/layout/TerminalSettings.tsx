import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createAuthenticApiClient, type PositionLimits } from '@/lib/authenticApi';
import { toast } from 'sonner';

const apiClient = createAuthenticApiClient();

export function TerminalSettings() {
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<PositionLimits>({
    maxPositionSize: 0.1,
    maxDailyLoss: 1.0,
    maxRiskPerTrade: 0.05,
  });

  const { data: configData, isLoading } = useQuery({
    queryKey: ['trading-config'],
    queryFn: () => apiClient.getTradingConfig(),
  });

  const { data: positionLimitsData } = useQuery({
    queryKey: ['position-limits'],
    queryFn: () => apiClient.getPositionLimits(),
  });

  const updateConfigMutation = useMutation({
    mutationFn: (config: PositionLimits) => apiClient.updatePositionLimits(config),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['position-limits'] });
      queryClient.invalidateQueries({ queryKey: ['trading-config'] });
      toast.success('Settings updated successfully');
      setIsEditing(false);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update settings: ${error.message}`);
    },
  });

  const handleInputChange = (field: keyof PositionLimits, value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setFormData(prev => ({ ...prev, [field]: numValue }));
    }
  };

  const handleSave = () => {
    updateConfigMutation.mutate(formData);
  };

  const handleCancel = () => {
    if (positionLimitsData?.data) {
      setFormData(positionLimitsData.data);
    }
    setIsEditing(false);
  };

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="text-green-400">Loading settings...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-green-400">Trading Settings</h2>
        {!isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            className="px-4 py-2 bg-green-600 text-black font-mono text-sm hover:bg-green-500 transition-colors"
          >
            EDIT
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-green-400 border-b border-green-600 pb-2">
            Position Limits
          </h3>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-green-400 mb-1">Max Position Size (SOL)</label>
              {isEditing ? (
                <input
                  type="number"
                  step="0.01"
                  value={formData.maxPositionSize}
                  onChange={(e) => handleInputChange('maxPositionSize', e.target.value)}
                  className="w-full px-3 py-2 bg-black border border-green-600 text-green-400 font-mono"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-800 border border-gray-600 text-green-400 font-mono">
                  {positionLimitsData?.data?.maxPositionSize || 0.1} SOL
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm text-green-400 mb-1">Max Daily Loss (SOL)</label>
              {isEditing ? (
                <input
                  type="number"
                  step="0.01"
                  value={formData.maxDailyLoss}
                  onChange={(e) => handleInputChange('maxDailyLoss', e.target.value)}
                  className="w-full px-3 py-2 bg-black border border-green-600 text-green-400 font-mono"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-800 border border-gray-600 text-green-400 font-mono">
                  {positionLimitsData?.data?.maxDailyLoss || 1.0} SOL
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm text-green-400 mb-1">Max Risk Per Trade (SOL)</label>
              {isEditing ? (
                <input
                  type="number"
                  step="0.01"
                  value={formData.maxRiskPerTrade}
                  onChange={(e) => handleInputChange('maxRiskPerTrade', e.target.value)}
                  className="w-full px-3 py-2 bg-black border border-green-600 text-green-400 font-mono"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-800 border border-gray-600 text-green-400 font-mono">
                  {positionLimitsData?.data?.maxRiskPerTrade || 0.05} SOL
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-green-400 border-b border-green-600 pb-2">
            System Status
          </h3>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-green-400 mb-1">Circuit Breaker</label>
              <div className="px-3 py-2 bg-gray-800 border border-gray-600 text-green-400 font-mono">
                {configData?.data?.circuitBreakerStatus || 'CLOSED'}
              </div>
            </div>

            <div>
              <label className="block text-sm text-green-400 mb-1">Trading Mode</label>
              <div className="px-3 py-2 bg-gray-800 border border-gray-600 text-green-400 font-mono">
                {configData?.data?.tradingMode || 'LIVE'}
              </div>
            </div>

            <div>
              <label className="block text-sm text-green-400 mb-1">Last Update</label>
              <div className="px-3 py-2 bg-gray-800 border border-gray-600 text-green-400 font-mono">
                {new Date().toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      </div>

      {isEditing && (
        <div className="flex space-x-4 pt-6 border-t border-green-600">
          <button
            onClick={handleSave}
            disabled={updateConfigMutation.isPending}
            className="px-6 py-2 bg-green-600 text-black font-mono hover:bg-green-500 transition-colors disabled:opacity-50"
          >
            {updateConfigMutation.isPending ? 'SAVING...' : 'SAVE'}
          </button>
          <button
            onClick={handleCancel}
            disabled={updateConfigMutation.isPending}
            className="px-6 py-2 border border-green-600 text-green-400 font-mono hover:bg-green-600 hover:text-black transition-colors disabled:opacity-50"
          >
            CANCEL
          </button>
        </div>
      )}
    </div>
  );
} 