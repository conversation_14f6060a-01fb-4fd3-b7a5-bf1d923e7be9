import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';

export function TerminalNavigation() {
  const location = useLocation();

  const navItems = [
    { path: '/', label: 'OPPORTUNITIES', icon: '📊' },
    { path: '/history', label: 'HISTORY', icon: '📈' },
    { path: '/settings', label: 'SETTINGS', icon: '⚙️' },
  ];

  return (
    <nav className="border-b border-green-600 bg-black">
      <div className="flex space-x-1 p-2">
        {navItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={cn(
              'px-4 py-2 text-sm font-mono border border-green-600 transition-colors',
              location.pathname === item.path
                ? 'bg-green-600 text-black'
                : 'text-green-400 hover:bg-green-600 hover:text-black'
            )}
          >
            <span className="mr-2">{item.icon}</span>
            {item.label}
          </Link>
        ))}
      </div>
    </nav>
  );
} 