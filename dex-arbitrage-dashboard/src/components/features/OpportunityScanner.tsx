import { useState, useMemo, useEffect, memo, useCallback, Suspense, lazy } from 'react';
import { toast } from 'sonner';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  useOpportunities, 
  useExecuteArbitrage,
  useTriggerScan
} from '@/hooks/useArbitrage';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { useSystemHealth } from '@/hooks/useSystemHealth';
import { useTradingVolume } from '@/hooks/useTradingVolume';
import { useResponsive } from '@/hooks/useResponsive';
import { Button } from '@/components/ui/button';
import { MobileFullButton, MobileTouchButton, EnhancedButton } from '@/components/ui/button-utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
// Skeleton components are defined inline for better performance
import { Form<PERSON>ield, FormSelectField } from '@/components/ui/form-field';
import { PerformanceMonitor } from '@/components/ui/performance-monitor';
import type { ArbitrageOpportunity, ArbitrageFilters } from '@/types/api';
import { 
  formatCurrency, 
  formatPercentage, 
  formatDuration,
  cn 
} from '@/lib/utils';
import { 
  filterFormSchema,
  sanitizeTokenSymbol,
  sanitizeNumber,
  type FilterFormData
} from '@/lib/validation';
import { 
  RefreshCwIcon, 
  FilterIcon, 
  SearchIcon, 
  TrendingUpIcon, 
  ClockIcon,
  InfoIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  XIcon,
  WifiIcon,
  ActivityIcon,
  BarChart3Icon
} from 'lucide-react';

// Lazy load expensive components for better initial load performance
const LazyFilterPanel = lazy(() => 
  Promise.resolve({ default: FilterPanel })
);

const LazyMobileOpportunityCard = lazy(() => 
  Promise.resolve({ default: MobileOpportunityCard })
);

const LazyMobileFilterPanel = lazy(() => 
  Promise.resolve({ default: MobileFilterPanel })
);

// Loading fallback components
const OpportunityTableSkeleton = () => (
  <div className="trading-card">
    <div className="animate-pulse">
      <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-12 bg-gray-100 dark:bg-gray-800 rounded mb-2"></div>
      ))}
    </div>
  </div>
);

const OpportunityCardSkeleton = () => (
  <div className="mobile-card">
    <div className="animate-pulse">
      <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-3"></div>
      <div className="space-y-2">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-4 bg-gray-100 dark:bg-gray-800 rounded"></div>
        ))}
      </div>
    </div>
  </div>
);

// === Opportunity Table Component ===

interface OpportunityTableProps {
  opportunities: ArbitrageOpportunity[];
  onExecute: (opportunity: ArbitrageOpportunity) => void;
  onSort: (field: keyof ArbitrageOpportunity) => void;
  sortField: keyof ArbitrageOpportunity | null;
  sortDirection: 'asc' | 'desc';
  isExecuting: boolean;
  executingIds: Set<string>;
}

const OpportunityTable = memo(function OpportunityTable({ 
  opportunities, 
  onExecute, 
  onSort, 
  sortField, 
  sortDirection,
  isExecuting,
  executingIds
}: OpportunityTableProps) {
  
  const getSortDirection = (field: keyof ArbitrageOpportunity) => {
    if (sortField !== field) return null;
    return sortDirection;
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'LOW': return 'text-green-600 dark:text-green-400';
      case 'MEDIUM': return 'text-yellow-600 dark:text-yellow-400';
      case 'HIGH': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600 dark:text-green-400';
      case 'EXECUTING': return 'text-blue-600 dark:text-blue-400';
      case 'EXPIRED': return 'text-gray-600 dark:text-gray-400';
      case 'ERROR': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const isOpportunityExecutable = (opportunity: ArbitrageOpportunity) => {
    return opportunity.status === 'ACTIVE' && 
           opportunity.timeToExpire > 0 && 
           !executingIds.has(opportunity.id);
  };

  if (opportunities.length === 0) {
    return (
      <div className="trading-card">
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="mb-4 text-muted-foreground">
            <SearchIcon className="h-12 w-12 mx-auto mb-2" />
            <h3 className="text-lg font-medium">No Opportunities Found</h3>
            <p className="text-sm">Try adjusting your filters or wait for new opportunities to appear.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="trading-card">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead 
              sortable 
              sortDirection={getSortDirection('pair')}
              onSort={() => onSort('pair')}
            >
              Token Pair
            </TableHead>
            <TableHead>Exchanges</TableHead>
            <TableHead 
              sortable 
              sortDirection={getSortDirection('profitAmount')}
              onSort={() => onSort('profitAmount')}
            >
              Profit
            </TableHead>
            <TableHead 
              sortable 
              sortDirection={getSortDirection('volume')}
              onSort={() => onSort('volume')}
            >
              Volume
            </TableHead>
            <TableHead 
              sortable 
              sortDirection={getSortDirection('confidence')}
              onSort={() => onSort('confidence')}
            >
              Confidence
            </TableHead>
            <TableHead 
              sortable 
              sortDirection={getSortDirection('riskLevel')}
              onSort={() => onSort('riskLevel')}
            >
              Risk
            </TableHead>
            <TableHead>Expires</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {opportunities.map((opportunity) => (
            <TableRow 
              key={opportunity.id}
              className={cn(
                "hover:bg-muted/50 transition-colors",
                opportunity.status === 'EXPIRED' && "opacity-60",
                executingIds.has(opportunity.id) && "bg-blue-50 dark:bg-blue-950/20"
              )}
            >
              <TableCell className="font-medium">
                <div className="flex flex-col">
                  <span className="font-semibold">
                    {opportunity.pair.baseToken.symbol}/{opportunity.pair.quoteToken.symbol}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {opportunity.pair.baseToken.name}
                  </span>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex flex-col text-xs">
                  <span className="text-green-600 dark:text-green-400">
                    Buy: {opportunity.buyExchange}
                  </span>
                  <span className="text-red-600 dark:text-red-400">
                    Sell: {opportunity.sellExchange}
                  </span>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex flex-col">
                  <span className={cn(
                    "font-semibold",
                    opportunity.profitAmount > 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
                  )}>
                    {formatCurrency(opportunity.profitAmount)}
                  </span>
                  <span className={cn(
                    "text-xs",
                    opportunity.profitPercent > 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
                  )}>
                    {formatPercentage(opportunity.profitPercent)}
                  </span>
                </div>
              </TableCell>
              
              <TableCell>
                <span className="font-mono text-sm">
                  {formatCurrency(opportunity.volume)}
                </span>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className={cn(
                        "h-2 rounded-full transition-all",
                        opportunity.confidence >= 0.8 ? "bg-green-500" :
                        opportunity.confidence >= 0.6 ? "bg-yellow-500" : "bg-red-500"
                      )}
                      style={{ width: `${opportunity.confidence * 100}%` }}
                    />
                  </div>
                  <span className="text-xs font-mono w-10">
                    {Math.round(opportunity.confidence * 100)}%
                  </span>
                </div>
              </TableCell>
              
              <TableCell>
                <span className={cn("font-medium text-xs px-2 py-1 rounded", getRiskColor(opportunity.riskLevel))}>
                  {opportunity.riskLevel}
                </span>
              </TableCell>
              
              <TableCell>
                <span className={cn(
                  "text-xs",
                  opportunity.timeToExpire <= 30 ? "text-red-600 dark:text-red-400" :
                  opportunity.timeToExpire <= 60 ? "text-yellow-600 dark:text-yellow-400" : 
                  "text-muted-foreground"
                )}>
                  {opportunity.timeToExpire > 0 ? formatDuration(opportunity.timeToExpire) : 'Expired'}
                </span>
              </TableCell>
              
              <TableCell>
                <span className={cn("font-medium text-xs", getStatusColor(opportunity.status))}>
                  {opportunity.status}
                </span>
              </TableCell>
              
              <TableCell className="text-right">
                <Button
                  size="sm"
                  variant={isOpportunityExecutable(opportunity) ? "default" : "secondary"}
                  disabled={!isOpportunityExecutable(opportunity) || isExecuting}
                  onClick={() => onExecute(opportunity)}
                  className="min-w-[80px]"
                >
                  {executingIds.has(opportunity.id) ? (
                    <>
                      <RefreshCwIcon className="h-3 w-3 animate-spin mr-1" />
                      Executing
                    </>
                  ) : (
                    'Execute'
                  )}
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
});

// === Filter Panel Component ===

interface FilterPanelProps {
  filters: ArbitrageFilters;
  onFiltersChange: (filters: ArbitrageFilters) => void;
  isVisible: boolean;
  onToggle: () => void;
}

const FilterPanel = memo(function FilterPanel({ filters, onFiltersChange, isVisible, onToggle }: FilterPanelProps) {
  const methods = useForm<FilterFormData>({
    resolver: zodResolver(filterFormSchema),
    defaultValues: {
      minProfit: filters.minProfit?.toString() || '',
      maxRisk: filters.maxRisk,
      exchange: filters.exchange,
      tokenSymbol: filters.tokenSymbol,
      status: filters.status,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder,
    },
  });

  const { handleSubmit, reset, formState: { isValid } } = methods;

  const onSubmit = (data: FilterFormData) => {
    const newFilters: ArbitrageFilters = {
      minProfit: data.minProfit ? Number(data.minProfit) : undefined,
      maxRisk: data.maxRisk,
      exchange: data.exchange,
      tokenSymbol: data.tokenSymbol,
      status: data.status,
      sortBy: data.sortBy,
      sortOrder: data.sortOrder,
    };
    onFiltersChange(newFilters);
    toast.success('Filters applied successfully');
  };

  const handleClearFilters = () => {
    reset();
    onFiltersChange({});
    toast.success('All filters cleared');
  };

  const exchangeOptions = [
    { value: 'jupiter', label: 'Jupiter' },
    { value: 'orca', label: 'Orca' },
    { value: 'meteora', label: 'Meteora' },
    { value: 'raydium', label: 'Raydium' },
    { value: 'serum', label: 'Serum' },
  ];

  const riskOptions = [
    { value: 'LOW', label: 'Low Risk Only' },
    { value: 'MEDIUM', label: 'Medium Risk or Lower' },
    { value: 'HIGH', label: 'High Risk or Lower' },
  ];

  const statusOptions = [
    { value: 'ACTIVE', label: 'Active Only' },
    { value: 'EXECUTING', label: 'Executing Only' },
    { value: 'EXPIRED', label: 'Expired Only' },
    { value: 'ERROR', label: 'Error Only' },
  ];

  const sortOptions = [
    { value: 'profit', label: 'Profit' },
    { value: 'volume', label: 'Volume' },
    { value: 'created', label: 'Created Date' },
    { value: 'confidence', label: 'Confidence' },
  ];

  const sortOrderOptions = [
    { value: 'desc', label: 'Descending' },
    { value: 'asc', label: 'Ascending' },
  ];

  return (
    <div className="trading-card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Filters</h3>
        <div className="flex gap-2">
          <Button variant="ghost" size="sm" onClick={handleClearFilters}>
            Clear All
          </Button>
          <Button variant="ghost" size="sm" onClick={onToggle}>
            <FilterIcon className="h-4 w-4 mr-2" />
            {isVisible ? 'Hide' : 'Show'} Filters
          </Button>
        </div>
      </div>
      
      {isVisible && (
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <FormField
                name="minProfit"
                label="Min Profit ($)"
                type="number"
                placeholder="0.00"
                min={0}
                max={1000000}
                step={0.01}
                sanitize={sanitizeNumber}
              />
              
              <FormSelectField
                name="maxRisk"
                label="Max Risk"
                options={riskOptions}
                placeholder="All Risk Levels"
              />
              
              <FormSelectField
                name="exchange"
                label="Exchange"
                options={exchangeOptions}
                placeholder="All Exchanges"
              />
              
              <FormField
                name="tokenSymbol"
                label="Token Symbol"
                placeholder="e.g. SOL, USDC"
                sanitize={sanitizeTokenSymbol}
              />
              
              <FormSelectField
                name="status"
                label="Status"
                options={statusOptions}
                placeholder="All Statuses"
              />
              
              <FormSelectField
                name="sortBy"
                label="Sort By"
                options={sortOptions}
                placeholder="Default"
              />
              
              <FormSelectField
                name="sortOrder"
                label="Sort Order"
                options={sortOrderOptions}
                placeholder="Descending"
              />
              
              <div className="flex items-end">
                <Button 
                  type="submit" 
                  disabled={!isValid}
                  className="w-full"
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          </form>
        </FormProvider>
      )}
    </div>
  );
});

// === Mobile Opportunity Card Component ===

interface MobileOpportunityCardProps {
  opportunity: ArbitrageOpportunity;
  onExecute: (opportunity: ArbitrageOpportunity) => void;
  isExecuting: boolean;
  executingIds: Set<string>;
}

const MobileOpportunityCard = memo(function MobileOpportunityCard({ 
  opportunity, 
  onExecute, 
  isExecuting,
  executingIds 
}: MobileOpportunityCardProps) {
  const isExecutable = opportunity.status === 'ACTIVE' && 
                      opportunity.timeToExpire > 0 && 
                      !executingIds.has(opportunity.id);
  
  const isCurrentlyExecuting = executingIds.has(opportunity.id);

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'LOW': return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'MEDIUM': return <AlertTriangleIcon className="w-4 h-4 text-yellow-500" />;
      case 'HIGH': return <AlertTriangleIcon className="w-4 h-4 text-red-500" />;
      default: return <InfoIcon className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'EXECUTING': return <RefreshCwIcon className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'EXPIRED': return <ClockIcon className="w-4 h-4 text-gray-500" />;
      case 'ERROR': return <XIcon className="w-4 h-4 text-red-500" />;
      default: return <InfoIcon className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="mobile-card">
      {/* Card Header */}
      <div className="mobile-card-header">
        <div className="flex items-center gap-2">
          <TrendingUpIcon className="w-5 h-5 text-green-500" />
          <span className="font-medium text-base">{opportunity.pair.baseToken.symbol}/{opportunity.pair.quoteToken.symbol}</span>
        </div>
        <div className="flex items-center gap-2">
          {getRiskIcon(opportunity.riskLevel)}
          <span className="text-sm">{opportunity.riskLevel}</span>
        </div>
      </div>

      {/* Card Content */}
      <div className="mobile-card-content">
        {/* Profit Section */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">Profit:</span>
          <span className={cn(
            "font-bold text-lg",
            opportunity.profitAmount > 0 ? "text-green-500" : "text-red-500"
          )}>
            {formatCurrency(opportunity.profitAmount)}
          </span>
        </div>

        {/* Amount Section */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">Amount:</span>
          <span className="font-medium">{formatCurrency(opportunity.volume)}</span>
        </div>

        {/* Time to Expire */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">Expires:</span>
          <div className="flex items-center gap-1">
            <ClockIcon className="w-4 h-4 text-gray-500" />
            <span className="text-sm">{formatDuration(opportunity.timeToExpire)}</span>
          </div>
        </div>

        {/* Status */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">Status:</span>
          <div className="flex items-center gap-1">
            {getStatusIcon(opportunity.status)}
            <span className="text-sm">{opportunity.status}</span>
          </div>
        </div>
      </div>

      {/* Card Footer */}
      <div className="mobile-card-footer">
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">ID: {opportunity.id.slice(0, 8)}...</span>
        </div>
        
        {isExecutable && (
          <EnhancedButton
            onClick={() => onExecute(opportunity)}
            disabled={isExecuting || isCurrentlyExecuting}
            loading={isCurrentlyExecuting}
            loadingText="Executing..."
            variant="default"
            size="sm"
          >
            Execute
          </EnhancedButton>
        )}
        
        {!isExecutable && (
          <Button
            disabled
            variant="outline"
            size="sm"
            className="opacity-50"
          >
            {opportunity.status === 'EXPIRED' ? 'Expired' : 'Unavailable'}
          </Button>
        )}
      </div>
    </div>
  );
});

// === Mobile Filter Panel Component ===

interface MobileFilterPanelProps {
  filters: ArbitrageFilters;
  onFiltersChange: (filters: ArbitrageFilters) => void;
  isVisible: boolean;
  onToggle: () => void;
}

const MobileFilterPanel = memo(function MobileFilterPanel({ filters, onFiltersChange, isVisible, onToggle }: MobileFilterPanelProps) {
  const [activeTab, setActiveTab] = useState<'basic' | 'advanced'>('basic');
  
  const methods = useForm<FilterFormData>({
    resolver: zodResolver(filterFormSchema),
    defaultValues: {
      minProfit: filters.minProfit?.toString() || '',
      maxRisk: filters.maxRisk,
      exchange: filters.exchange,
      tokenSymbol: filters.tokenSymbol,
      status: filters.status,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder,
    },
  });

  const { handleSubmit, reset, formState: { isValid } } = methods;

  const onSubmit = (data: FilterFormData) => {
    const newFilters: ArbitrageFilters = {
      minProfit: data.minProfit ? Number(data.minProfit) : undefined,
      maxRisk: data.maxRisk,
      exchange: data.exchange,
      tokenSymbol: data.tokenSymbol,
      status: data.status,
      sortBy: data.sortBy,
      sortOrder: data.sortOrder,
    };
    onFiltersChange(newFilters);
    toast.success('Filters applied successfully');
    onToggle();
  };

  const handleClearFilters = () => {
    reset();
    onFiltersChange({});
    toast.success('All filters cleared');
  };

  const exchangeOptions = [
    { value: 'jupiter', label: 'Jupiter' },
    { value: 'orca', label: 'Orca' },
    { value: 'meteora', label: 'Meteora' },
    { value: 'raydium', label: 'Raydium' },
    { value: 'serum', label: 'Serum' },
  ];

  const riskOptions = [
    { value: 'LOW', label: 'Low Only' },
    { value: 'MEDIUM', label: 'Medium & Below' },
    { value: 'HIGH', label: 'All Risks' },
  ];

  const statusOptions = [
    { value: 'ACTIVE', label: 'Active Only' },
    { value: 'EXECUTING', label: 'Executing Only' },
    { value: 'EXPIRED', label: 'Expired Only' },
    { value: 'ERROR', label: 'Error Only' },
  ];

  if (!isVisible) return null;

  return (
    <div className="mobile-filter-panel">
      {/* Filter Header */}
      <div className="mobile-filter-header">
        <h3 className="text-lg font-medium">Filters</h3>
        <MobileTouchButton
          onClick={onToggle}
          variant="ghost"
                      size="icon"
        >
          <XIcon className="w-5 h-5" />
        </MobileTouchButton>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-700 mb-4">
        <button
          onClick={() => setActiveTab('basic')}
          className={cn(
            "flex-1 py-2 text-center border-b-2 transition-colors",
            activeTab === 'basic' 
              ? "border-green-500 text-green-500" 
              : "border-transparent text-gray-500"
          )}
        >
          Basic
        </button>
        <button
          onClick={() => setActiveTab('advanced')}
          className={cn(
            "flex-1 py-2 text-center border-b-2 transition-colors",
            activeTab === 'advanced' 
              ? "border-green-500 text-green-500" 
              : "border-transparent text-gray-500"
          )}
        >
          Advanced
        </button>
      </div>

      {/* Filter Content */}
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {activeTab === 'basic' && (
            <>
              <FormField
                name="minProfit"
                label="Min Profit ($)"
                type="number"
                placeholder="0.00"
                min={0}
                max={1000000}
                step={0.01}
                sanitize={sanitizeNumber}
              />

              <FormSelectField
                name="maxRisk"
                label="Max Risk"
                options={riskOptions}
                placeholder="Any Risk"
              />
            </>
          )}

          {activeTab === 'advanced' && (
            <>
              <FormSelectField
                name="exchange"
                label="Exchange"
                options={exchangeOptions}
                placeholder="All Exchanges"
              />

              <FormField
                name="tokenSymbol"
                label="Token Symbol"
                placeholder="SOL, USDC, etc."
                sanitize={sanitizeTokenSymbol}
              />

              <FormSelectField
                name="status"
                label="Status"
                options={statusOptions}
                placeholder="All Statuses"
              />
            </>
          )}

          {/* Filter Actions */}
          <div className="flex gap-3 mt-6">
            <MobileFullButton
              onClick={handleClearFilters}
              variant="outline"
            >
              Clear All
            </MobileFullButton>
            <MobileFullButton
              type="submit"
              disabled={!isValid}
              variant="default"
            >
              Apply Filters
            </MobileFullButton>
          </div>
        </form>
      </FormProvider>
    </div>
  );
});

// === Main OpportunityScanner Component ===

export function OpportunityScanner() {
  const { isMobile } = useResponsive();
  const [filters, setFilters] = useState<ArbitrageFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [sortField, setSortField] = useState<keyof ArbitrageOpportunity | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [executingIds, setExecutingIds] = useState<Set<string>>(new Set());
  
  // Monitoring hooks
  const networkStatus = useNetworkStatus();
  const systemHealth = useSystemHealth();
  const tradingVolume = useTradingVolume();
  
  const { data: opportunitiesData, isLoading } = useOpportunities(filters, { realtime: true });
  const executeArbitrage = useExecuteArbitrage();
  const triggerScan = useTriggerScan();

  const opportunities = useMemo(() => 
    opportunitiesData?.data?.data || [], 
    [opportunitiesData?.data?.data]
  );

  // Start monitoring when component mounts
  useEffect(() => {
    tradingVolume.startMonitoring();
    return () => {
      tradingVolume.stopMonitoring();
    };
  }, [tradingVolume]);

  const handleSort = useCallback((field: keyof ArbitrageOpportunity) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  }, [sortField, sortDirection]);

  const handleExecute = useCallback(async (opportunity: ArbitrageOpportunity) => {
    if (executingIds.has(opportunity.id)) return;
    
    setExecutingIds(prev => new Set(prev).add(opportunity.id));
    
    try {
      await executeArbitrage.mutateAsync(opportunity);
      toast.success(`✅ Executed arbitrage for ${opportunity.pair.baseToken.symbol}/${opportunity.pair.quoteToken.symbol}`, {
        description: `Profit: ${formatCurrency(opportunity.netProfit)}`
      });
    } catch (error) {
      toast.error(`❌ Failed to execute arbitrage`, {
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setExecutingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(opportunity.id);
        return newSet;
      });
    }
  }, [executingIds, executeArbitrage]);

  const handleManualScan = useCallback(async () => {
    try {
      await triggerScan.mutateAsync();
      toast.success('✅ Manual scan triggered', {
        description: 'Searching for new arbitrage opportunities...'
      });
    } catch (error) {
      toast.error('❌ Failed to trigger manual scan', {
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  }, [triggerScan]);

  const handleFiltersChange = useCallback((newFilters: ArbitrageFilters) => {
    setFilters(newFilters);
  }, []);

  const handleToggleFilters = useCallback(() => {
    setShowFilters(prev => !prev);
  }, []);

  const sortedOpportunities = useMemo(() => {
    if (!sortField) return opportunities;
    
    return [...opportunities].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      return 0;
    });
  }, [opportunities, sortField, sortDirection]);

  if (isMobile) {
    return (
      <PerformanceMonitor componentName="OpportunityScanner-Mobile">
        <div className="p-4 space-y-4">
          {/* Mobile Header */}
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold">Arbitrage Opportunities</h1>
            <div className="flex gap-2">
              {/* Status Indicators */}
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${
                  networkStatus.isConnected ? 'bg-green-500' : 
                  networkStatus.isDegraded ? 'bg-yellow-500' : 'bg-red-500'
                }`} />
                <div className={`w-2 h-2 rounded-full ${
                  systemHealth.isHealthy ? 'bg-green-500' : 
                  systemHealth.isDegraded ? 'bg-yellow-500' : 'bg-red-500'
                }`} />
                <div className={`w-2 h-2 rounded-full ${
                  tradingVolume.isVolumeHealthyForTrading ? 'bg-green-500' : 'bg-yellow-500'
                }`} />
              </div>
              <EnhancedButton
                onClick={handleManualScan}
                disabled={triggerScan.isPending}
                loading={triggerScan.isPending}
                loadingText="Scanning..."
                size="sm"
                variant="outline"
            >
              <RefreshCwIcon className="w-4 h-4" />
            </EnhancedButton>
                        <Button
              onClick={handleToggleFilters}
              size="sm"
              variant="outline"
            >
              <FilterIcon className="w-4 h-4" />
            </Button>
            </div>
          </div>

          {/* Mobile Filter Panel */}
          <Suspense fallback={<div className="mobile-card p-4">Loading filters...</div>}>
            <LazyMobileFilterPanel
              filters={filters}
              onFiltersChange={handleFiltersChange}
              isVisible={showFilters}
              onToggle={handleToggleFilters}
            />
          </Suspense>

          {/* Mobile Opportunities */}
          {isLoading ? (
            <OpportunityCardSkeleton />
          ) : sortedOpportunities.length === 0 ? (
            <div className="mobile-card">
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <SearchIcon className="w-12 h-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900">No Opportunities Found</h3>
                <p className="text-sm text-gray-500 mt-2">
                  Try adjusting your filters or wait for new opportunities to appear.
                </p>
              </div>
            </div>
          ) : (
            <Suspense fallback={<OpportunityCardSkeleton />}>
              <div className="space-y-4">
                {sortedOpportunities.map((opportunity) => (
                  <LazyMobileOpportunityCard
                    key={opportunity.id}
                    opportunity={opportunity}
                    onExecute={handleExecute}
                    isExecuting={executingIds.has(opportunity.id)}
                    executingIds={executingIds}
                  />
                ))}
              </div>
            </Suspense>
          )}
        </div>
      </PerformanceMonitor>
    );
  }

  return (
    <PerformanceMonitor componentName="OpportunityScanner-Desktop">
      <div className="p-6 space-y-6">
        {/* Desktop Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Arbitrage Opportunities</h1>
            <p className="text-gray-600 mt-1">
              Real-time arbitrage opportunities across DEX exchanges
            </p>
            {/* Status Indicators */}
            <div className="flex items-center gap-4 mt-2">
              <div className="flex items-center gap-2">
                <WifiIcon className="w-4 h-4" />
                <span className="text-sm">Network:</span>
                <span className={`text-sm font-medium ${
                  networkStatus.isConnected ? 'text-green-600' : 
                  networkStatus.isDegraded ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {networkStatus.status}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <ActivityIcon className="w-4 h-4" />
                <span className="text-sm">System:</span>
                <span className={`text-sm font-medium ${
                  systemHealth.isHealthy ? 'text-green-600' : 
                  systemHealth.isDegraded ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {systemHealth.level}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <BarChart3Icon className="w-4 h-4" />
                <span className="text-sm">Volume:</span>
                <span className={`text-sm font-medium ${
                  tradingVolume.isVolumeHealthyForTrading ? 'text-green-600' : 'text-yellow-600'
                }`}>
                  {tradingVolume.volumeTrend}
                </span>
              </div>
            </div>
          </div>
          <div className="flex gap-3">
            <EnhancedButton
              onClick={handleManualScan}
              disabled={triggerScan.isPending}
              loading={triggerScan.isPending}
              loadingText="Scanning..."
              variant="default"
            >
              <RefreshCwIcon className="w-4 h-4" />
              Manual Scan
            </EnhancedButton>
            <Button
              onClick={handleToggleFilters}
              variant="outline"
            >
              <FilterIcon className="w-4 h-4" />
              Filters
            </Button>
          </div>
        </div>

        {/* Desktop Filter Panel */}
        <Suspense fallback={<div className="trading-card p-4">Loading filters...</div>}>
          <LazyFilterPanel
            filters={filters}
            onFiltersChange={handleFiltersChange}
            isVisible={showFilters}
            onToggle={handleToggleFilters}
          />
        </Suspense>

        {/* Desktop Opportunities Table */}
        <div className="trading-card">
          {isLoading ? (
            <div className="p-6">
              <OpportunityTableSkeleton />
            </div>
                      ) : (
              <Suspense fallback={<OpportunityTableSkeleton />}>
                <OpportunityTable
                  opportunities={sortedOpportunities}
                  onExecute={handleExecute}
                  onSort={handleSort}
                  sortField={sortField}
                  sortDirection={sortDirection}
                  isExecuting={executingIds.size > 0}
                  executingIds={executingIds}
                />
              </Suspense>
            )}
        </div>
      </div>
    </PerformanceMonitor>
  );
}

export default OpportunityScanner; 