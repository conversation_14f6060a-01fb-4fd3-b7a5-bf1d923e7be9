import React, { memo } from 'react';
import { Button } from '@/components/ui/button';
import { EnhancedButton } from '@/components/ui/button-utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  TrendingUpIcon, 
  ClockIcon
} from 'lucide-react';
import type { ArbitrageOpportunity } from '@/types/api';
import { formatCurrency, formatPercentage, formatDuration } from '@/lib/utils';

interface OpportunityTableProps {
  opportunities: ArbitrageOpportunity[];
  onExecute: (opportunity: ArbitrageOpportunity) => void;
  onSort: (field: keyof ArbitrageOpportunity) => void;
  sortField: keyof ArbitrageOpportunity | null;
  sortDirection: 'asc' | 'desc';
  isExecuting: boolean;
  executingIds: Set<string>;
}

export const OpportunityTable = memo(function OpportunityTable({ 
  opportunities, 
  onExecute, 
  onSort, 
  sortField, 
  sortDirection,
  isExecuting,
  executingIds
}: OpportunityTableProps) {



  const getRiskColor = (risk: string) => {
    switch (risk?.toUpperCase()) {
      case 'LOW': return 'text-green-600 bg-green-50';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50';
      case 'HIGH': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'ACTIVE': return 'text-green-600 bg-green-50';
      case 'EXECUTING': return 'text-blue-600 bg-blue-50';
      case 'EXPIRED': return 'text-gray-600 bg-gray-50';
      case 'ERROR': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const isOpportunityExecutable = (opportunity: ArbitrageOpportunity) => {
    return opportunity.status === 'ACTIVE' && 
           opportunity.profitPercent > 0.1 && 
           opportunity.riskLevel !== 'HIGH';
  };

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead 
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => onSort('pair' as keyof ArbitrageOpportunity)}
            >
              <div className="flex items-center gap-1">
                Token
                {sortField === 'pair' && (
                  <span className="text-xs">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => onSort('profitPercent')}
            >
              <div className="flex items-center gap-1">
                Profit %
                {sortField === 'profitPercent' && (
                  <span className="text-xs">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => onSort('profitAmount')}
            >
              <div className="flex items-center gap-1">
                Profit Amount
                {sortField === 'profitAmount' && (
                  <span className="text-xs">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => onSort('riskLevel')}
            >
              <div className="flex items-center gap-1">
                Risk
                {sortField === 'riskLevel' && (
                  <span className="text-xs">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => onSort('buyExchange')}
            >
              <div className="flex items-center gap-1">
                Exchange
                {sortField === 'buyExchange' && (
                  <span className="text-xs">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => onSort('status')}
            >
              <div className="flex items-center gap-1">
                Status
                {sortField === 'status' && (
                  <span className="text-xs">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => onSort('timeToExpire')}
            >
              <div className="flex items-center gap-1">
                Expires
                {sortField === 'timeToExpire' && (
                  <span className="text-xs">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            </TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {opportunities.map((opportunity) => {
            const isCurrentlyExecuting = executingIds.has(opportunity.id);
            const isExecutable = isOpportunityExecutable(opportunity);
            
            return (
              <TableRow key={opportunity.id} className="hover:bg-gray-50">
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold">
                        {opportunity.pair.baseToken.symbol?.charAt(0) || '?'}
                      </span>
                    </div>
                    <div>
                      <div className="font-semibold">{opportunity.pair.baseToken.symbol}</div>
                      <div className="text-xs text-gray-500">{opportunity.pair.baseToken.name}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <TrendingUpIcon className="w-4 h-4 text-green-600" />
                    <span className="font-semibold text-green-600">
                      {formatPercentage(opportunity.profitPercent)}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="font-semibold">
                    {formatCurrency(opportunity.profitAmount)}
                  </span>
                </TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(opportunity.riskLevel)}`}>
                    {opportunity.riskLevel}
                  </span>
                </TableCell>
                <TableCell>
                  <span className="font-medium">{opportunity.buyExchange}</span>
                </TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(opportunity.status)}`}>
                    {opportunity.status}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <ClockIcon className="w-4 h-4" />
                    {formatDuration(opportunity.timeToExpire)}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  {isExecutable ? (
                    <EnhancedButton
                      onClick={() => onExecute(opportunity)}
                      disabled={isExecuting || isCurrentlyExecuting}
                      loading={isCurrentlyExecuting}
                      loadingText="Executing..."
                      size="sm"
                      variant="default"
                    >
                      Execute
                    </EnhancedButton>
                  ) : (
                    <Button
                      disabled
                      variant="outline"
                      size="sm"
                      className="opacity-50"
                    >
                      {opportunity.status === 'EXPIRED' ? 'Expired' : 'Unavailable'}
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}); 