
import { useResponsive } from '@/hooks/useResponsive';
import { useTradingHistory } from '@/hooks/useArbitrage';
import { formatCurrency, formatDuration, cn } from '@/lib/utils';
import { TradingHistorySkeleton } from '@/components/ui/opportunity-skeleton';
import { CheckCircleIcon, XIcon, ClockIcon, TrendingUpIcon } from 'lucide-react';
import type { TradeRecord } from '@/types/api';

// === Mobile Trade Card Component ===

interface MobileTradeCardProps {
  trade: TradeRecord;
}

function MobileTradeCard({ trade }: MobileTradeCardProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'FAILED': return <XIcon className="w-4 h-4 text-red-500" />;
      case 'PENDING': return <ClockIcon className="w-4 h-4 text-yellow-500" />;
      default: return <ClockIcon className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="mobile-card">
      {/* Card Header */}
      <div className="mobile-card-header">
        <div className="flex items-center gap-2">
          <TrendingUpIcon className="w-5 h-5 text-green-500" />
          <span className="font-medium text-base">{trade.pair.baseToken.symbol}/{trade.pair.quoteToken.symbol}</span>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(trade.status)}
          <span className="text-sm">{trade.status}</span>
        </div>
      </div>

      {/* Card Content */}
      <div className="mobile-card-content">
        {/* Profit Section */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">Profit:</span>
          <span className={cn(
            "font-bold text-lg",
            trade.netProfit > 0 ? "text-green-500" : "text-red-500"
          )}>
            {formatCurrency(trade.netProfit)}
          </span>
        </div>

        {/* Exchanges */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">Route:</span>
          <span className="font-medium text-sm">{trade.buyExchange} → {trade.sellExchange}</span>
        </div>

        {/* Date */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">Date:</span>
          <span className="text-sm">{new Date(trade.startTime).toLocaleDateString()}</span>
        </div>

        {/* Duration */}
        {trade.endTime && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">Duration:</span>
            <span className="text-sm">
              {formatDuration(new Date(trade.endTime).getTime() - new Date(trade.startTime).getTime())}
            </span>
          </div>
        )}
      </div>

      {/* Card Footer */}
      <div className="mobile-card-footer">
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">ID: {trade.id.slice(0, 8)}...</span>
        </div>
        
        {trade.gasUsed && (
          <div className="text-xs text-gray-500">
            Gas: {trade.gasUsed.toLocaleString()}
          </div>
        )}
      </div>
    </div>
  );
}

function TradingHistory() {
  const { isMobile } = useResponsive();
  const { data: historyData, isLoading } = useTradingHistory();

  if (isLoading) {
    if (isMobile) {
      return (
        <div className="p-4">
          <TradingHistorySkeleton />
        </div>
      );
    }

    return (
      <div className="p-6">
        <TradingHistorySkeleton />
      </div>
    );
  }

  const trades = historyData?.data || [];
  const tradesArray = Array.isArray(trades) ? trades : (trades && 'data' in trades ? trades.data : []);

  if (tradesArray.length === 0) {
    if (isMobile) {
      return (
        <div className="p-4">
          <div className="mobile-card">
            <div className="text-center py-8">
              <TrendingUpIcon className="w-12 h-12 mx-auto mb-4 text-gray-500" />
              <h3 className="text-lg font-medium mb-2">No Trading History</h3>
              <p className="text-sm text-gray-500">Execute some trades to see history here.</p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="ascii-box">
        <pre className="ascii-header">
{`┌─ TRADING HISTORY ─────────────────────────────────────────────────────────────┐
│ No trading history found.                                                     │
│ Execute some trades to see history here.                                      │
└────────────────────────────────────────────────────────────────────────────────┘`}
        </pre>
      </div>
    );
  }

  // Mobile Layout
  if (isMobile) {
    return (
      <div className="space-y-4 p-4">
        {/* Mobile Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold">Trading History</h2>
            <p className="text-sm text-gray-500">{tradesArray.length} trades</p>
          </div>
        </div>

        {/* Mobile Trade Cards */}
        <div className="space-y-2">
          {tradesArray.map((trade: TradeRecord) => (
            <MobileTradeCard key={trade.id} trade={trade} />
          ))}
        </div>
      </div>
    );
  }

  // Desktop Layout
  return (
    <div className="ascii-box">
      <pre className="ascii-header">
{`┌─ TRADING HISTORY ─────────────────────────────────────────────────────────────┐`}
      </pre>
      <table className="terminal-table w-full">
        <thead>
          <tr>
            <th>ID</th>
            <th>PAIR</th>
            <th>TYPE</th>
            <th>BUY</th>
            <th>SELL</th>
            <th>PROFIT</th>
            <th>GAS</th>
            <th>STATUS</th>
            <th>TIME</th>
          </tr>
        </thead>
        <tbody>
          {tradesArray.slice(0, 10).map((trade: TradeRecord) => (
            <tr key={trade.id}>
              <td className="ascii-dim">{trade.id?.slice(-8) || 'N/A'}</td>
              <td className="ascii-blue">
                {trade.pair?.baseToken?.symbol || 'UNK'}/{trade.pair?.quoteToken?.symbol || 'UNK'}
              </td>
              <td className="ascii-dim">{trade.type || 'ARBITRAGE'}</td>
              <td className="ascii-dim">{trade.buyExchange || 'N/A'}</td>
              <td className="ascii-dim">{trade.sellExchange || 'N/A'}</td>
              <td className={trade.netProfit > 0 ? 'ascii-success' : 'ascii-error'}>
                ${(trade.netProfit || 0).toFixed(4)}
              </td>
              <td className="ascii-dim">${(trade.gasCost || 0).toFixed(4)}</td>
              <td className={trade.status === 'COMPLETED' ? 'ascii-success' : 'ascii-error'}>
                {trade.status || 'UNKNOWN'}
              </td>
              <td className="ascii-dim">
                {trade.startTime ? new Date(trade.startTime).toLocaleTimeString() : 'N/A'}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <pre className="ascii-header">
{`└────────────────────────────────────────────────────────────────────────────────┘`}
      </pre>
    </div>
  );
}

export default TradingHistory; 