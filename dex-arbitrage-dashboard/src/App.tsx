import { useEffect } from 'react';
import { initializeWebSocket, wsQueryHelpers } from '@/lib/websocket';
import { useResponsive } from '@/hooks/useResponsive';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { TradingErrorBoundary } from '@/components/error/TradingErrorBoundary';
import { NetworkErrorBoundary } from '@/components/error/NetworkErrorBoundary';
import { ExecutionErrorBoundary } from '@/components/error/ExecutionErrorBoundary';
import { AuthProvider } from '@/contexts/AuthContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Routes, Route } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/sonner';
import { TerminalDashboard } from '@/components/layout/TerminalDashboard';
import { OpportunityScanner } from '@/components/features/OpportunityScanner';
import TradingHistory from '@/components/features/TradingHistory';
import { TerminalSettings } from '@/components/layout/TerminalSettings';
import { MobileHeader } from '@/components/layout/MobileHeader';
import { MobileNavigation } from '@/components/layout/MobileNavigation';

function App() {
  const queryClient = useQueryClient();
  const { isMobile } = useResponsive();

  // Initialize WebSocket connection and setup query integration
  useEffect(() => {
    try {
      // Setup WebSocket integration with React Query
      wsQueryHelpers.setupQueryInvalidation(queryClient);
      wsQueryHelpers.setupOptimisticUpdates(queryClient);
      
      // Initialize WebSocket connection with error handling
      initializeWebSocket();
    } catch (error) {
      console.warn('WebSocket initialization failed, continuing without real-time updates:', error);
      // Don't let WebSocket failures block the app
    }
  }, [queryClient]);

  // Mobile Layout
  if (isMobile) {
    return (
      <ErrorBoundary>
        <AuthProvider>
          <NetworkErrorBoundary>
            <ExecutionErrorBoundary>
              <TradingErrorBoundary>
                <ProtectedRoute requiredRole="viewer">
                <div className="min-h-screen bg-gray-50">
                  <Toaster />
                  <div className="container mx-auto px-4 py-8">
                    <MobileHeader />
                    
                    <main className="pb-20"> {/* Add bottom padding for mobile nav */}
                      <Routes>
                        <Route path="/" element={<OpportunityScanner />} />
                        <Route path="/opportunities" element={<OpportunityScanner />} />
                        <Route path="/history" element={<TradingHistory />} />
                        <Route path="/settings" element={<TerminalSettings />} />
                      </Routes>
                    </main>
                    
                    <MobileNavigation />
                  </div>
                </div>
              </ProtectedRoute>
            </TradingErrorBoundary>
          </ExecutionErrorBoundary>
        </NetworkErrorBoundary>
      </AuthProvider>
      </ErrorBoundary>
    );
  }

  // Desktop Layout
  return (
    <ErrorBoundary>
      <AuthProvider>
        <NetworkErrorBoundary>
          <ExecutionErrorBoundary>
            <TradingErrorBoundary>
              <ProtectedRoute requiredRole="viewer">
                <TerminalDashboard />
              </ProtectedRoute>
            </TradingErrorBoundary>
          </ExecutionErrorBoundary>
        </NetworkErrorBoundary>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
