import { useCallback, useRef, useEffect, useState, useMemo } from 'react';

interface PerformanceOptimizationOptions {
  debounceMs?: number;
  throttleMs?: number;
  maxCacheSize?: number;
  cleanupIntervalMs?: number;
}

interface CacheEntry<T> {
  key: string;
  value: T;
  timestamp: number;
  accessCount: number;
}

export function usePerformanceOptimization(options: PerformanceOptimizationOptions = {}) {
  const {
    debounceMs = 300,
    throttleMs = 100,
    maxCacheSize = 100,
    cleanupIntervalMs = 60000 // 1 minute
  } = options;

  const cacheRef = useRef<Map<string, CacheEntry<unknown>>>(new Map());
  const debounceTimersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const throttleTimersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // LRU Cache implementation
  const getFromCache = useCallback(<T>(key: string): T | null => {
    const entry = cacheRef.current.get(key);
    if (entry) {
      entry.accessCount++;
      entry.timestamp = Date.now();
      return entry.value as T;
    }
    return null;
  }, []);

  const setCache = useCallback(<T>(key: string, value: T): void => {
    // Remove oldest entries if cache is full
    if (cacheRef.current.size >= maxCacheSize) {
      const entries = Array.from(cacheRef.current.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      const oldestKey = entries[0][0];
      cacheRef.current.delete(oldestKey);
    }

    cacheRef.current.set(key, {
      key,
      value,
      timestamp: Date.now(),
      accessCount: 1
    });
  }, [maxCacheSize]);

  const clearCache = useCallback((): void => {
    cacheRef.current.clear();
  }, []);

  // Debounced function
  const debounce = useCallback(<T extends (...args: unknown[]) => unknown>(
    key: string,
    fn: T,
    delay: number = debounceMs
  ): T => {
    return ((...args: Parameters<T>) => {
      const existingTimer = debounceTimersRef.current.get(key);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }

      const timer = setTimeout(() => {
        fn(...args);
        debounceTimersRef.current.delete(key);
      }, delay);

      debounceTimersRef.current.set(key, timer);
    }) as T;
  }, [debounceMs]);

  // Throttled function
  const throttle = useCallback(<T extends (...args: unknown[]) => unknown>(
    key: string,
    fn: T,
    delay: number = throttleMs
  ): T => {
    return ((...args: Parameters<T>) => {
      if (throttleTimersRef.current.has(key)) {
        return;
      }

      fn(...args);

      const timer = setTimeout(() => {
        throttleTimersRef.current.delete(key);
      }, delay);

      throttleTimersRef.current.set(key, timer);
    }) as T;
  }, [throttleMs]);

  // Periodic cache cleanup
  useEffect(() => {
    const cleanup = () => {
      const now = Date.now();
      const entries = Array.from(cacheRef.current.entries());
      
      // Remove entries older than 5 minutes or with low access count
      entries.forEach(([key, entry]) => {
        const age = now - entry.timestamp;
        if (age > 300000 || entry.accessCount < 2) { // 5 minutes or accessed less than 2 times
          cacheRef.current.delete(key);
        }
      });
    };

    const interval = setInterval(cleanup, cleanupIntervalMs);
    return () => clearInterval(interval);
  }, [cleanupIntervalMs]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Capture ref values to avoid cleanup warnings
      // These refs contain Maps, not React nodes
      // eslint-disable-next-line react-hooks/exhaustive-deps
      const debounceTimers = debounceTimersRef.current;
      // eslint-disable-next-line react-hooks/exhaustive-deps
      const throttleTimers = throttleTimersRef.current;
      // eslint-disable-next-line react-hooks/exhaustive-deps
      const cache = cacheRef.current;
      
      // Clear all timers
      debounceTimers.forEach(timer => clearTimeout(timer));
      debounceTimers.clear();
      
      throttleTimers.forEach(timer => clearTimeout(timer));
      throttleTimers.clear();
      
      // Clear cache
      cache.clear();
    };
  }, []);

  return {
    getFromCache,
    setCache,
    clearCache,
    debounce,
    throttle,
    cacheSize: cacheRef.current.size,
    maxCacheSize
  };
}

// Virtual scrolling hook for large datasets
export function useVirtualScrolling<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleRange = useMemo(() => {
    const start = Math.floor(scrollTop / itemHeight);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const end = Math.min(start + visibleCount + overscan, items.length);
    const startIndex = Math.max(0, start - overscan);
    
    return { start: startIndex, end };
  }, [scrollTop, itemHeight, containerHeight, overscan, items.length]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end);
  }, [items, visibleRange]);

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.start * itemHeight;

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    visibleRange
  };
} 