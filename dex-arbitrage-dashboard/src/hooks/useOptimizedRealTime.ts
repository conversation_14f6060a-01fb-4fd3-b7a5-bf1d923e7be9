import { useMemo, useEffect, useRef, useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useWebSocketConnection } from './useArbitrage';

// === Performance Configuration ===

interface RealTimeConfig {
  wsEnabled: boolean;
  pollingFallback: boolean;
  wsStaleTime: number;
  pollingStaleTime: number;
  wsRefetchInterval: number | false;
  pollingRefetchInterval: number;
  maxRetries: number;
  retryDelay: number;
}

// === Performance Metrics ===

interface PerformanceMetrics {
  wsLatency: number;
  pollingLatency: number;
  wsSuccessRate: number;
  pollingSuccessRate: number;
  lastWsUpdate: Date | null;
  lastPollingUpdate: Date | null;
  connectionQuality: 'EXCELLENT' | 'GOOD' | 'POOR' | 'FAILED';
}

// === Optimized Real-Time Hook ===

// Default configuration - moved outside to avoid recreation
const DEFAULT_REAL_TIME_CONFIG: RealTimeConfig = {
  wsEnabled: true,
  pollingFallback: true,
  wsStaleTime: 5000, // 5 seconds with WebSocket
  pollingStaleTime: 10000, // 10 seconds with polling
  wsRefetchInterval: false, // No polling when WS is active
  pollingRefetchInterval: 15000, // 15 seconds polling fallback
  maxRetries: 3,
  retryDelay: 1000,
};

export function useOptimizedRealTime(
  config: Partial<RealTimeConfig> = {}
): {
  isWsConnected: boolean;
  shouldUsePolling: boolean;
  optimizedConfig: {
    refetchInterval: number | false;
    staleTime: number;
    enabled: boolean;
  };
  performance: PerformanceMetrics;
  forcePolling: () => void;
  forceWebSocket: () => void;
} {

  const finalConfig = useMemo(() => ({ ...DEFAULT_REAL_TIME_CONFIG, ...config }), [config]);
  const { isConnected: wsConnected, connectionHealth } = useWebSocketConnection();
  
  // Performance tracking
  const performanceRef = useRef<PerformanceMetrics>({
    wsLatency: 0,
    pollingLatency: 0,
    wsSuccessRate: 1.0,
    pollingSuccessRate: 1.0,
    lastWsUpdate: null,
    lastPollingUpdate: null,
    connectionQuality: 'EXCELLENT',
  });

  // Force mode overrides
  const forceModeRef = useRef<'ws' | 'polling' | null>(null);

  // Calculate connection quality
  const connectionQuality = useMemo(() => {
    const health = connectionHealth;
    
    if (!wsConnected) return 'FAILED';
    
    const timeSinceHeartbeat = health.lastHeartbeat ? Date.now() - health.lastHeartbeat.getTime() : null;
    if (timeSinceHeartbeat === null) return 'EXCELLENT';
    
    if (timeSinceHeartbeat < 30000) return 'EXCELLENT'; // < 30s
    if (timeSinceHeartbeat < 60000) return 'GOOD'; // < 1min
    if (timeSinceHeartbeat < 120000) return 'POOR'; // < 2min
    return 'FAILED';
  }, [wsConnected, connectionHealth]);

  // Determine if we should use polling
  const shouldUsePolling = useMemo(() => {
    // Force mode takes precedence
    if (forceModeRef.current === 'polling') return true;
    if (forceModeRef.current === 'ws') return false;
    
    // WebSocket is preferred if available and healthy
    if (wsConnected && connectionQuality !== 'FAILED') {
      return false;
    }
    
    // Fall back to polling if WebSocket is unavailable or poor quality
    return !wsConnected || connectionQuality === 'FAILED';
  }, [wsConnected, connectionQuality]);

  // Optimized configuration
  const optimizedConfig = useMemo(() => {
    if (shouldUsePolling) {
      return {
        refetchInterval: finalConfig.pollingRefetchInterval,
        staleTime: finalConfig.pollingStaleTime,
        enabled: true,
      };
    } else {
      return {
        refetchInterval: finalConfig.wsRefetchInterval,
        staleTime: finalConfig.wsStaleTime,
        enabled: true,
      };
    }
  }, [shouldUsePolling, finalConfig]);

  // Update performance metrics
  useEffect(() => {
    performanceRef.current.connectionQuality = connectionQuality;
  }, [connectionQuality]);

  // Force mode controls
  const forcePolling = () => {
    forceModeRef.current = 'polling';
  };

  const forceWebSocket = () => {
    forceModeRef.current = 'ws';
  };

  return {
    isWsConnected: wsConnected,
    shouldUsePolling,
    optimizedConfig,
    performance: performanceRef.current,
    forcePolling,
    forceWebSocket,
  };
}

// === Enhanced Query Hook with Optimization ===

export function useOptimizedQuery<T>(
  queryFn: () => Promise<T>,
  options: {
    queryKey: readonly unknown[];
    realtime?: boolean;
    config?: Partial<RealTimeConfig>;
  }
) {
  const { realtime = true, config } = options;
  const { optimizedConfig, isWsConnected, shouldUsePolling } = useOptimizedRealTime(config);

  // Use React Query with optimized configuration
  const query = useQuery({
    queryKey: options.queryKey,
    queryFn,
    ...optimizedConfig,
    enabled: realtime && optimizedConfig.enabled,
  });

  return {
    ...query,
    isWsConnected,
    shouldUsePolling,
    realtimeMode: shouldUsePolling ? 'polling' : 'websocket',
  };
}

// === WebSocket Health Monitor ===

export function useWebSocketHealthMonitor() {
  const { connectionHealth, isConnected } = useWebSocketConnection();
  const [healthHistory, setHealthHistory] = useState<Array<{
    timestamp: Date;
    isConnected: boolean;
    latency: number | null;
  }>>([]);

  useEffect(() => {
    const interval = setInterval(() => {
      setHealthHistory(prev => {
        const newEntry = {
          timestamp: new Date(),
          isConnected,
          latency: connectionHealth.lastHeartbeat ? Date.now() - connectionHealth.lastHeartbeat.getTime() : null,
        };
        
        // Keep last 100 entries
        return [...prev.slice(-99), newEntry];
      });
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [isConnected, connectionHealth.lastHeartbeat]);

  const connectionUptime = useMemo(() => {
    if (healthHistory.length === 0) return 0;
    
    const connectedEntries = healthHistory.filter(entry => entry.isConnected);
    return (connectedEntries.length / healthHistory.length) * 100;
  }, [healthHistory]);

  const averageLatency = useMemo(() => {
    const latencies = healthHistory
      .map(entry => entry.latency)
      .filter((latency): latency is number => latency !== null);
    
    if (latencies.length === 0) return null;
    
    return latencies.reduce((sum, latency) => sum + latency, 0) / latencies.length;
  }, [healthHistory]);

  return {
    connectionUptime,
    averageLatency,
    healthHistory,
    currentHealth: connectionHealth,
  };
}

// === Performance Analytics ===

export function usePerformanceAnalytics() {
  const { connectionUptime, averageLatency } = useWebSocketHealthMonitor();
  const [analytics, setAnalytics] = useState({
    totalQueries: 0,
    wsQueries: 0,
    pollingQueries: 0,
    averageQueryTime: 0,
    cacheHitRate: 0,
  });

  const trackQuery = useCallback((mode: 'ws' | 'polling', duration: number) => {
    setAnalytics(prev => ({
      ...prev,
      totalQueries: prev.totalQueries + 1,
      wsQueries: prev.wsQueries + (mode === 'ws' ? 1 : 0),
      pollingQueries: prev.pollingQueries + (mode === 'polling' ? 1 : 0),
      averageQueryTime: (prev.averageQueryTime * prev.totalQueries + duration) / (prev.totalQueries + 1),
    }));
  }, []);

  const performanceScore = useMemo(() => {
    const uptimeScore = connectionUptime / 100;
    const latencyScore = averageLatency ? Math.max(0, 1 - (averageLatency / 60000)) : 1; // 1 minute max
    const queryEfficiencyScore = analytics.wsQueries / Math.max(1, analytics.totalQueries);
    
    return (uptimeScore + latencyScore + queryEfficiencyScore) / 3;
  }, [connectionUptime, averageLatency, analytics]);

  return {
    analytics,
    performanceScore,
    trackQuery,
    recommendations: getPerformanceRecommendations(performanceScore, analytics),
  };
}

function getPerformanceRecommendations(score: number, analytics: { totalQueries: number; wsQueries: number; pollingQueries: number; averageQueryTime: number; cacheHitRate: number }) {
  const recommendations = [];
  
  if (score < 0.7) {
    recommendations.push('Consider switching to polling mode for better reliability');
  }
  
  if (analytics.pollingQueries > analytics.wsQueries) {
    recommendations.push('WebSocket connection may be unstable - check network');
  }
  
  if (analytics.averageQueryTime > 2000) {
    recommendations.push('Query performance is slow - consider optimizing API calls');
  }
  
  return recommendations;
} 