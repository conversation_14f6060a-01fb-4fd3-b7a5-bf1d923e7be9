import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { notifications } from '@/lib/notifications';
import type { VolumeAlertType } from '@/lib/notifications';

interface VolumeData {
  timestamp: Date;
  volume: number;
  token?: string;
  exchange?: string;
}

interface VolumeThresholds {
  highVolume: number;
  lowVolume: number;
  unusualActivityThreshold: number; // percentage change
}

interface VolumeAlert {
  id: string;
  type: VolumeAlertType;
  volume: number;
  threshold: number;
  token?: string;
  timestamp: Date;
  acknowledged: boolean;
}

interface TradingVolumeConfig {
  checkInterval?: number; // milliseconds
  historyLength?: number; // number of data points to keep
  thresholds?: Partial<VolumeThresholds>;
  enableAlerts?: boolean;
  mobileOptimized?: boolean; // NEW: Mobile optimization flag
}

const DEFAULT_THRESHOLDS: Required<VolumeThresholds> = {
  highVolume: 1000000, // $1M
  lowVolume: 10000, // $10K
  unusualActivityThreshold: 50, // 50% change
};

// Mobile-optimized defaults
const MOBILE_CONFIG = {
  checkInterval: 120000, // 2 minutes on mobile (vs 30s on desktop)
  historyLength: 20, // Keep fewer data points on mobile
  enableAlerts: false, // Disable alerts on mobile to reduce overhead
};

const DEFAULT_CONFIG: Required<TradingVolumeConfig> = {
  checkInterval: 30000, // 30 seconds
  historyLength: 100, // Keep last 100 data points
  thresholds: DEFAULT_THRESHOLDS,
  enableAlerts: true,
  mobileOptimized: false,
};

// Detect mobile device
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         window.innerWidth <= 768;
};

export const useTradingVolume = (config: TradingVolumeConfig = {}) => {
  // Apply mobile optimizations if on mobile or explicitly requested
  const isMobile = isMobileDevice();
  const shouldUseMobileConfig = isMobile || config.mobileOptimized;
  
  const finalConfig = { 
    ...DEFAULT_CONFIG, 
    ...(shouldUseMobileConfig ? MOBILE_CONFIG : {}),
    ...config 
  };
  
  const thresholds = useMemo(() => ({ ...DEFAULT_THRESHOLDS, ...config.thresholds }), [config.thresholds]);
  
  const [volumeHistory, setVolumeHistory] = useState<VolumeData[]>([]);
  const [alerts, setAlerts] = useState<VolumeAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const lastAlertTime = useRef<Record<string, number>>({});
  const abortControllerRef = useRef<AbortController | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate volume statistics
  const calculateVolumeStats = useCallback(() => {
    if (volumeHistory.length === 0) {
      return {
        currentVolume: 0,
        averageVolume: 0,
        maxVolume: 0,
        minVolume: 0,
        volumeChange: 0,
        volumeChangePercent: 0,
      };
    }

    const currentVolume = volumeHistory[volumeHistory.length - 1].volume;
    const previousVolume = volumeHistory.length > 1 
      ? volumeHistory[volumeHistory.length - 2].volume 
      : currentVolume;
    
    const volumes = volumeHistory.map(v => v.volume);
    const averageVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
    const maxVolume = Math.max(...volumes);
    const minVolume = Math.min(...volumes);
    const volumeChange = currentVolume - previousVolume;
    const volumeChangePercent = previousVolume > 0 
      ? ((volumeChange / previousVolume) * 100) 
      : 0;

    return {
      currentVolume,
      averageVolume,
      maxVolume,
      minVolume,
      volumeChange,
      volumeChangePercent,
    };
  }, [volumeHistory]);

  // Check volume alerts with rate limiting (disabled on mobile)
  const checkVolumeAlerts = useCallback((volumeData: VolumeData) => {
    if (!finalConfig.enableAlerts || shouldUseMobileConfig) return;

    const stats = calculateVolumeStats();
    const { volume } = volumeData;
    const { token } = volumeData;
    const newAlerts: VolumeAlert[] = [];

    // Rate limiting: Only show alerts every 30 seconds for the same type
    const now = Date.now();
    const rateLimitKey = (type: string) => `${type}_${token || 'global'}`;
    const lastAlertTimeRef = lastAlertTime.current;
    const lastAlertTimeValue = lastAlertTimeRef[rateLimitKey('volume')] || 0;
    const timeSinceLastAlert = now - lastAlertTimeValue;

    // High volume alert
    if (volume > thresholds.highVolume) {
      const alertId = `high_volume_${token || 'global'}_${volumeData.timestamp.getTime()}`;
      
      // Only alert if enough time has passed (30 seconds)
      if (timeSinceLastAlert > 30000) {
        newAlerts.push({
          id: alertId,
          type: 'high_volume',
          volume,
          threshold: thresholds.highVolume,
          token,
          timestamp: volumeData.timestamp,
          acknowledged: false,
        });

        notifications.volumeAlert(
          'high_volume', 
          volume, 
          thresholds.highVolume, 
          token
        );
        lastAlertTimeRef[rateLimitKey('volume')] = now;
      }
    }

    // Low volume alert
    if (volume < thresholds.lowVolume) {
      const alertId = `low_volume_${token || 'global'}_${volumeData.timestamp.getTime()}`;
      
      // Only alert if enough time has passed (30 seconds)
      if (timeSinceLastAlert > 30000) {
        newAlerts.push({
          id: alertId,
          type: 'low_volume',
          volume,
          threshold: thresholds.lowVolume,
          token,
          timestamp: volumeData.timestamp,
          acknowledged: false,
        });

        notifications.volumeAlert(
          'low_volume', 
          volume, 
          thresholds.lowVolume, 
          token
        );
        lastAlertTimeRef[rateLimitKey('volume')] = now;
      }
    }

    // Unusual activity alert
    if (Math.abs(stats.volumeChangePercent) > thresholds.unusualActivityThreshold) {
      const alertId = `unusual_activity_${token || 'global'}_${volumeData.timestamp.getTime()}`;
      const lastUnusualAlert = lastAlertTimeRef[rateLimitKey('unusual')] || 0;
      const timeSinceLastUnusualAlert = now - lastUnusualAlert;

      // Only alert if enough time has passed (2 minutes for unusual activity)
      if (timeSinceLastUnusualAlert > 120000) {
        newAlerts.push({
          id: alertId,
          type: 'unusual_activity',
          volume,
          threshold: thresholds.unusualActivityThreshold,
          token,
          timestamp: volumeData.timestamp,
          acknowledged: false,
        });

        notifications.volumeAlert(
          'unusual_activity', 
          volume, 
          thresholds.unusualActivityThreshold, 
          token
        );
        lastAlertTimeRef[rateLimitKey('unusual')] = now;
      }
    }

    // Add new alerts to state
    if (newAlerts.length > 0) {
      setAlerts(prev => [...prev, ...newAlerts].slice(-20)); // Keep last 20 alerts
    }
  }, [finalConfig.enableAlerts, shouldUseMobileConfig, calculateVolumeStats, thresholds]);

  // Fetch current volume data with mobile optimization
  const fetchVolumeData = useCallback(async () => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Clear any pending retry
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    setIsLoading(true);
    setError(null);
    
    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    try {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001/api';
      const response = await fetch(`${apiBaseUrl}/trading/volume`, {
        method: 'GET',
        cache: 'no-cache',
        signal: abortControllerRef.current.signal,
        // Add timeout for mobile
        ...(shouldUseMobileConfig && {
          // Mobile-specific fetch options
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      // Check if the response has the expected structure
      if (!result.success || !result.data) {
        throw new Error('Invalid response format');
      }

      const data = result.data;
      const volumeData: VolumeData = {
        timestamp: new Date(),
        volume: data.totalVolume || 0,
        token: data.token,
        exchange: data.exchange,
      };

      setVolumeHistory(prev => {
        const newHistory = [...prev, volumeData].slice(-finalConfig.historyLength);
        return newHistory;
      });

      checkVolumeAlerts(volumeData);

    } catch (error) {
      // Handle abort errors silently
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      
      // Only log error, don't show toast for every failed request
      console.warn('Failed to fetch volume data:', errorMessage);
      
      // Retry logic for mobile (less aggressive)
      if (shouldUseMobileConfig && !retryTimeoutRef.current) {
        retryTimeoutRef.current = setTimeout(() => {
          fetchVolumeData();
        }, 30000); // 30 second retry on mobile
      }
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [finalConfig.historyLength, checkVolumeAlerts, shouldUseMobileConfig]);

  // Start monitoring with mobile optimization
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return; // Prevent multiple starts
    
    setIsMonitoring(true);
    fetchVolumeData(); // Initial fetch
  }, [fetchVolumeData, isMonitoring]);

  // Stop monitoring with cleanup
  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
    
    // Cancel ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    
    // Clear retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  // Periodic volume checks with mobile optimization
  useEffect(() => {
    if (!isMonitoring) return;

    const intervalId = setInterval(fetchVolumeData, finalConfig.checkInterval);

    return () => {
      clearInterval(intervalId);
      // Cleanup on unmount
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [isMonitoring, fetchVolumeData, finalConfig.checkInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  // Acknowledge alert
  const acknowledgeAlert = useCallback((alertId: string) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId 
          ? { ...alert, acknowledged: true }
          : alert
      )
    );
  }, []);

  // Dismiss alert
  const dismissAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  }, []);

  // Get volume trend
  const getVolumeTrend = useCallback(() => {
    if (volumeHistory.length < 2) return 'stable';

    const recent = volumeHistory.slice(-5); // Last 5 data points
    const older = volumeHistory.slice(-10, -5); // Previous 5 data points

    const recentAvg = recent.reduce((sum, v) => sum + v.volume, 0) / recent.length;
    const olderAvg = older.reduce((sum, v) => sum + v.volume, 0) / older.length;

    const change = ((recentAvg - olderAvg) / olderAvg) * 100;

    if (change > 10) return 'increasing';
    if (change < -10) return 'decreasing';
    return 'stable';
  }, [volumeHistory]);

  // Get unacknowledged alerts
  const getUnacknowledgedAlerts = useCallback(() => {
    return alerts.filter(alert => !alert.acknowledged);
  }, [alerts]);

  // Check if volume is healthy for trading
  const isVolumeHealthyForTrading = useCallback(() => {
    const stats = calculateVolumeStats();
    return (
      stats.currentVolume > thresholds.lowVolume &&
      stats.currentVolume < thresholds.highVolume * 10 && // Not extremely high
      Math.abs(stats.volumeChangePercent) < thresholds.unusualActivityThreshold * 2
    );
  }, [calculateVolumeStats, thresholds]);

  const stats = calculateVolumeStats();

  return {
    // State
    volumeHistory,
    alerts,
    isMonitoring,
    isLoading,
    error,
    
    // Computed values
    stats,
    volumeTrend: getVolumeTrend(),
    unacknowledgedAlerts: getUnacknowledgedAlerts(),
    isVolumeHealthyForTrading: isVolumeHealthyForTrading(),
    
    // Actions
    startMonitoring,
    stopMonitoring,
    fetchVolumeData,
    acknowledgeAlert,
    dismissAlert,
    
    // Utility
    hasData: volumeHistory.length > 0,
    alertCount: alerts.length,
    unacknowledgedCount: getUnacknowledgedAlerts().length,
    isMobileOptimized: shouldUseMobileConfig,
  };
}; 