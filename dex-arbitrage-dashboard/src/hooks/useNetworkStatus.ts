import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { notifications } from '@/lib/notifications';

interface NetworkState {
  status: NetworkStatus;
  isOnline: boolean;
  latency: number | null;
  lastChecked: Date;
  errorCount: number;
}

type NetworkStatus = 'connected' | 'disconnected' | 'degraded' | 'reconnecting';

interface NetworkConfig {
  checkInterval?: number; // milliseconds
  maxRetries?: number;
  timeout?: number; // milliseconds
  mobileOptimized?: boolean; // NEW: Mobile optimization flag
}

// Mobile-optimized defaults
const MOBILE_CONFIG = {
  checkInterval: 120000, // 2 minutes on mobile (vs 30s on desktop)
  maxRetries: 2, // Fewer retries on mobile
  timeout: 10000, // 10 second timeout on mobile
};

const DEFAULT_CONFIG: Required<NetworkConfig> = {
  checkInterval: 30000, // 30 seconds
  maxRetries: 3,
  timeout: 5000, // 5 seconds
  mobileOptimized: false,
};

// Detect mobile device
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         window.innerWidth <= 768;
};

export const useNetworkStatus = (config: NetworkConfig = {}) => {
  // Apply mobile optimizations if on mobile or explicitly requested
  const isMobile = isMobileDevice();
  const shouldUseMobileConfig = isMobile || config.mobileOptimized;
  
  const finalConfig = useMemo(() => ({ 
    ...DEFAULT_CONFIG, 
    ...(shouldUseMobileConfig ? MOBILE_CONFIG : {}),
    ...config 
  }), [shouldUseMobileConfig, config]);

  const [networkState, setNetworkState] = useState<NetworkState>({
    status: 'connected',
    isOnline: navigator.onLine,
    latency: null,
    lastChecked: new Date(),
    errorCount: 0,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check network status with mobile optimization
  const checkNetworkStatus = useCallback(async () => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Clear any pending retry
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    setIsLoading(true);
    setError(null);
    
    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), finalConfig.timeout);

      const response = await fetch('/api/health', {
        method: 'GET',
        signal: controller.signal,
        cache: 'no-cache',
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const latency = Date.now() - startTime;
      
      setNetworkState(prev => {
        const newStatus: NetworkStatus = 'connected';
        
        // Show notification for status changes (disabled on mobile)
        if (!shouldUseMobileConfig && newStatus !== prev.status) {
          notifications.network(newStatus, 'Network connection restored');
        }

        return {
          ...prev,
          lastChecked: new Date(),
          latency,
          errorCount: 0,
          status: newStatus,
        };
      });

    } catch (error) {
      const latency = Date.now() - startTime;
      
      setNetworkState(prev => {
        const newErrorCount = prev.errorCount + 1;
        const newStatus: NetworkStatus = newErrorCount >= finalConfig.maxRetries 
          ? 'disconnected' 
          : 'degraded';

        // Show notification for status changes (disabled on mobile)
        if (!shouldUseMobileConfig && newStatus !== prev.status) {
          const errorMessage = error instanceof Error ? error.message : 'Network error';
          notifications.network(newStatus, errorMessage);
        }

        return {
          ...prev,
          lastChecked: new Date(),
          latency,
          errorCount: newErrorCount,
          status: newStatus,
        };
      });
      
      const errorMessage = error instanceof Error ? error.message : 'Network error';
      setError(errorMessage);
      
      // Retry logic for mobile (less aggressive)
      if (shouldUseMobileConfig && !retryTimeoutRef.current) {
        retryTimeoutRef.current = setTimeout(() => {
          checkNetworkStatus();
        }, 30000); // 30 second retry on mobile
      }
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [finalConfig, shouldUseMobileConfig]);

  // Monitor online/offline events
  useEffect(() => {
    const handleOnline = () => {
      setNetworkState(prev => {
        if (prev.status !== 'connected') {
          if (!shouldUseMobileConfig) {
            notifications.network('connected', 'Browser connection restored');
          }
        }
        return {
          ...prev,
          isOnline: true,
          status: 'connected',
          errorCount: 0,
        };
      });
    };

    const handleOffline = () => {
      setNetworkState(prev => {
        if (!shouldUseMobileConfig) {
          notifications.network('disconnected', 'Browser connection lost');
        }
        return {
          ...prev,
          isOnline: false,
          status: 'disconnected',
        };
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [shouldUseMobileConfig]);

  // Periodic health checks with mobile optimization
  useEffect(() => {
    // Initial check
    checkNetworkStatus();

    // Set up interval
    const intervalId = setInterval(checkNetworkStatus, finalConfig.checkInterval);

    return () => {
      clearInterval(intervalId);
      // Cleanup on unmount
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [checkNetworkStatus, finalConfig.checkInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  // Manual retry function
  const retryConnection = useCallback(() => {
    setNetworkState(prev => ({
      ...prev,
      status: 'reconnecting',
    }));
    
    if (!shouldUseMobileConfig) {
      notifications.network('reconnecting');
    }
    
    // Attempt to reconnect
    setTimeout(() => {
      checkNetworkStatus();
    }, 1000);
  }, [checkNetworkStatus, shouldUseMobileConfig]);

  // Get connection quality indicator
  const getConnectionQuality = useCallback(() => {
    if (!networkState.latency) return 'unknown';
    
    if (networkState.latency < 100) return 'excellent';
    if (networkState.latency < 300) return 'good';
    if (networkState.latency < 1000) return 'fair';
    return 'poor';
  }, [networkState.latency]);

  // Check if system is healthy for trading
  const isHealthyForTrading = useCallback(() => {
    return (
      networkState.isOnline &&
      networkState.status === 'connected' &&
      networkState.errorCount === 0 &&
      (networkState.latency === null || networkState.latency < 2000)
    );
  }, [networkState]);

  return {
    // State
    status: networkState.status,
    isOnline: networkState.isOnline,
    latency: networkState.latency,
    lastChecked: networkState.lastChecked,
    errorCount: networkState.errorCount,
    isLoading,
    error,
    
    // Computed values
    connectionQuality: getConnectionQuality(),
    isHealthyForTrading: isHealthyForTrading(),
    
    // Actions
    retryConnection,
    checkNetworkStatus,
    
    // Utility
    isConnected: networkState.status === 'connected',
    isDisconnected: networkState.status === 'disconnected',
    isDegraded: networkState.status === 'degraded',
    isReconnecting: networkState.status === 'reconnecting',
    isMobileOptimized: shouldUseMobileConfig,
  };
}; 