import { useCallback } from 'react';
import { useAuth } from '@/contexts/auth-hooks';

export function useLogin() {
  const { login, isLoading } = useAuth();

  const handleLogin = useCallback(async (email: string, password: string) => {
    return await login(email, password);
  }, [login]);

  return {
    login: handleLogin,
    isLoading,
  };
}

export function useLogout() {
  const { logout } = useAuth();

  const handleLogout = useCallback(() => {
    logout();
  }, [logout]);

  return {
    logout: handleLogout,
  };
}

export function useRoleAccess() {
  const { hasRole, user, isAuthenticated } = useAuth();

  const canView = useCallback(() => {
    return isAuthenticated && hasRole('viewer');
  }, [isAuthenticated, hasRole]);

  const canTrade = useCallback(() => {
    return isAuthenticated && hasRole('trader');
  }, [isAuthenticated, hasRole]);

  const canAdmin = useCallback(() => {
    return isAuthenticated && hasRole('admin');
  }, [isAuthenticated, hasRole]);

  return {
    canView,
    canTrade,
    canAdmin,
    user,
    isAuthenticated,
  };
}

export function useAuthStatus() {
  const { user, token, isLoading, isAuthenticated } = useAuth();

  return {
    user,
    token,
    isLoading,
    isAuthenticated,
  };
} 