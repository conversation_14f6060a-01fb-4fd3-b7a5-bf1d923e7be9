import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { notifications } from '@/lib/notifications';

interface SystemHealthState {
  level: SystemHealthLevel;
  lastChecked: Date;
  components: Record<string, ComponentHealth>;
  overallScore: number; // 0-100
  alerts: SystemAlert[];
}

interface ComponentHealth {
  status: 'healthy' | 'degraded' | 'critical' | 'unknown';
  lastChecked: Date;
  responseTime: number | null;
  errorCount: number;
  details?: string;
}

interface SystemAlert {
  id: string;
  level: SystemHealthLevel;
  component: string;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
}

type SystemHealthLevel = 'healthy' | 'degraded' | 'critical' | 'unknown';

interface SystemHealthConfig {
  checkInterval?: number; // milliseconds
  timeout?: number; // milliseconds
  criticalThreshold?: number; // response time in ms
  degradedThreshold?: number; // response time in ms
  mobileOptimized?: boolean; // NEW: Mobile optimization flag
}

// System components to monitor
const SYSTEM_COMPONENTS = [
  { name: 'database', endpoint: '/api/health/db' },
  { name: 'trading', endpoint: '/api/health/trading' },
  { name: 'scanner', endpoint: '/api/health/scanner' },
  { name: 'providers', endpoint: '/api/health/providers' },
];

// Mobile-optimized defaults
const MOBILE_CONFIG = {
  checkInterval: 300000, // 5 minutes on mobile (vs 30s on desktop)
  timeout: 10000, // 10 second timeout on mobile
  criticalThreshold: 5000, // 5 seconds
  degradedThreshold: 2000, // 2 seconds
};

const DEFAULT_CONFIG: Required<SystemHealthConfig> = {
  checkInterval: 30000, // 30 seconds
  timeout: 5000, // 5 seconds
  criticalThreshold: 3000, // 3 seconds
  degradedThreshold: 1000, // 1 second
  mobileOptimized: false,
};

// Detect mobile device
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         window.innerWidth <= 768;
};

export const useSystemHealth = (config: SystemHealthConfig = {}) => {
  // Apply mobile optimizations if on mobile or explicitly requested
  const isMobile = isMobileDevice();
  const shouldUseMobileConfig = isMobile || config.mobileOptimized;
  
  const finalConfig = useMemo(() => ({ 
    ...DEFAULT_CONFIG, 
    ...(shouldUseMobileConfig ? MOBILE_CONFIG : {}),
    ...config 
  }), [shouldUseMobileConfig, config]);

  const [healthState, setHealthState] = useState<SystemHealthState>({
    level: 'unknown',
    lastChecked: new Date(),
    components: {},
    overallScore: 0,
    alerts: [],
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check individual component health
  const checkComponentHealth = useCallback(async (_componentName: string, endpoint: string): Promise<ComponentHealth> => {
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), finalConfig.timeout);

      const response = await fetch(endpoint, {
        method: 'GET',
        signal: controller.signal,
        cache: 'no-cache',
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const responseTime = Date.now() - startTime;
      
      let status: ComponentHealth['status'];
      if (responseTime < finalConfig.degradedThreshold) {
        status = 'healthy';
      } else if (responseTime < finalConfig.criticalThreshold) {
        status = 'degraded';
      } else {
        status = 'critical';
      }

      return {
        status,
        lastChecked: new Date(),
        responseTime,
        errorCount: 0,
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      return {
        status: 'critical',
        lastChecked: new Date(),
        responseTime,
        errorCount: 1,
        details: errorMessage,
      };
    }
  }, [finalConfig]);

  // Check overall system health with mobile optimization
  const checkSystemHealth = useCallback(async () => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Clear any pending retry
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    setIsLoading(true);
    setError(null);
    
    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    try {
      const componentChecks = await Promise.allSettled(
        SYSTEM_COMPONENTS.map(async (component) => {
          const health = await checkComponentHealth(component.name, component.endpoint);
          return { name: component.name, health };
        })
      );

      const newComponents: Record<string, ComponentHealth> = {};
      const newAlerts: SystemAlert[] = [];
      let criticalCount = 0;
      let degradedCount = 0;

      componentChecks.forEach((result) => {
        if (result.status === 'fulfilled') {
          const { name, health } = result.value;
          newComponents[name] = health;

          // Count statuses
          switch (health.status) {
            case 'critical':
              criticalCount++;
              break;
            case 'degraded':
              degradedCount++;
              break;
            case 'healthy':
              // healthyCount++; // Removed unused variable
              break;
          }

          // Create alerts for critical and degraded components (disabled on mobile)
          if (!shouldUseMobileConfig && (health.status === 'critical' || health.status === 'degraded')) {
            const alertId = `${name}_${health.lastChecked.getTime()}`;
            const existingAlert = healthState.alerts.find(a => a.id === alertId);
            
            if (!existingAlert) {
              newAlerts.push({
                id: alertId,
                level: health.status === 'critical' ? 'critical' : 'degraded',
                component: name,
                message: `${name} is ${health.status}${health.details ? `: ${health.details}` : ''}`,
                timestamp: health.lastChecked,
                acknowledged: false,
              });
            }
          }
        }
      });

      // Calculate overall health level
      let overallLevel: SystemHealthLevel;
      let overallScore: number;

      if (criticalCount > 0) {
        overallLevel = 'critical';
        overallScore = Math.max(0, 100 - (criticalCount * 25));
      } else if (degradedCount > 0) {
        overallLevel = 'degraded';
        overallScore = Math.max(25, 100 - (degradedCount * 15));
      } else {
        overallLevel = 'healthy';
        overallScore = 100;
      }

      // Update state
      setHealthState(prev => ({
        level: overallLevel,
        lastChecked: new Date(),
        components: { ...prev.components, ...newComponents },
        overallScore,
        alerts: [...prev.alerts, ...newAlerts].slice(-20), // Keep last 20 alerts
      }));

      // Show notifications for status changes (disabled on mobile)
      if (!shouldUseMobileConfig) {
        const prevLevel = healthState.level;
        if (overallLevel !== prevLevel) {
          notifications.systemHealth(overallLevel, `${overallLevel} system health detected`);
        }
      }

    } catch (error) {
      // Handle abort errors silently
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      
      console.warn('Failed to check system health:', errorMessage);
      
      // Retry logic for mobile (less aggressive)
      if (shouldUseMobileConfig && !retryTimeoutRef.current) {
        retryTimeoutRef.current = setTimeout(() => {
          checkSystemHealth();
        }, 60000); // 1 minute retry on mobile
      }
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [checkComponentHealth, healthState.alerts, healthState.level, shouldUseMobileConfig]);

  // Start monitoring with mobile optimization
  const startMonitoring = useCallback(() => {
    checkSystemHealth(); // Initial check
  }, [checkSystemHealth]);

  // Stop monitoring with cleanup
  const stopMonitoring = useCallback(() => {
    // Cancel ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    
    // Clear retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  // Periodic health checks with mobile optimization
  useEffect(() => {
    // Initial check
    checkSystemHealth();

    // Set up interval
    const intervalId = setInterval(checkSystemHealth, finalConfig.checkInterval);

    return () => {
      clearInterval(intervalId);
      // Cleanup on unmount
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [checkSystemHealth, finalConfig.checkInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  // Manual retry function
  const retryConnection = useCallback(() => {
    checkSystemHealth();
  }, [checkSystemHealth]);

  // Acknowledge alert
  const acknowledgeAlert = useCallback((alertId: string) => {
    setHealthState(prev => ({
      ...prev,
      alerts: prev.alerts.map(alert => 
        alert.id === alertId 
          ? { ...alert, acknowledged: true }
          : alert
      ),
    }));
  }, []);

  // Dismiss alert
  const dismissAlert = useCallback((alertId: string) => {
    setHealthState(prev => ({
      ...prev,
      alerts: prev.alerts.filter(alert => alert.id !== alertId),
    }));
  }, []);

  // Get unacknowledged alerts
  const getUnacknowledgedAlerts = useCallback(() => {
    return healthState.alerts.filter(alert => !alert.acknowledged);
  }, [healthState.alerts]);

  // Check if system is healthy for trading
  const isHealthyForTrading = useCallback(() => {
    return (
      healthState.level === 'healthy' &&
      healthState.overallScore >= 80 &&
      Object.values(healthState.components).every(comp => comp.status !== 'critical')
    );
  }, [healthState]);

  return {
    // State
    level: healthState.level,
    lastChecked: healthState.lastChecked,
    components: healthState.components,
    overallScore: healthState.overallScore,
    alerts: healthState.alerts,
    isLoading,
    error,
    
    // Computed values
    unacknowledgedAlerts: getUnacknowledgedAlerts(),
    isHealthyForTrading: isHealthyForTrading(),
    
    // Actions
    startMonitoring,
    stopMonitoring,
    retryConnection,
    acknowledgeAlert,
    dismissAlert,
    
    // Utility
    isHealthy: healthState.level === 'healthy',
    isDegraded: healthState.level === 'degraded',
    isCritical: healthState.level === 'critical',
    alertCount: healthState.alerts.length,
    unacknowledgedCount: getUnacknowledgedAlerts().length,
    isMobileOptimized: shouldUseMobileConfig,
  };
}; 