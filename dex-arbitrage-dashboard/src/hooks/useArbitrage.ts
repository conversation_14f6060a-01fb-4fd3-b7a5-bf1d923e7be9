import {
  useQuery,
  useMutation,
  useInfiniteQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useEffect, useState, useCallback, useMemo } from 'react';
import type {
  ArbitrageOpportunity,
  ArbitrageFilters,
  TradeHistoryFilters,
  TimeRange,
  ExportOptions,
} from '@/types/api';
import { arbitrageQueries, arbitrageMutations, systemQueries, systemMutations } from '@/lib/queryOptions';
import { wsClient, ConnectionState } from '@/lib/websocket';

// === Configuration ===

interface UseArbitrageConfig {
  realtime?: boolean;
  enabled?: boolean;
  refetchInterval?: number | false;
}

// === Arbitrage Hooks ===

export function useOpportunities(
  filters?: ArbitrageFilters,
  config: UseArbitrageConfig = {}
) {
  const { realtime = false, enabled = true, refetchInterval } = config;
  const queryClient = useQueryClient();

  const query = useQuery({
    ...arbitrageQueries.opportunities(filters),
    enabled,
    refetchInterval: refetchInterval !== undefined 
      ? refetchInterval 
      : realtime 
        ? 30000 // 🚀 OPTIMIZED: 30 seconds instead of 15 (WebSocket handles real-time updates)
        : false,
    staleTime: 10000, // 🚀 NEW: Consider data stale after 10s to encourage WebSocket updates
  });

  // Setup real-time updates
  useEffect(() => {
    if (!realtime || !enabled) return;

    const unsubscribe = wsClient.subscribeToArbitrageOpportunities(() => {
      queryClient.invalidateQueries({ 
        queryKey: arbitrageQueries.opportunities(filters).queryKey 
      });
    });

    return unsubscribe;
  }, [queryClient, filters, realtime, enabled]);

  return query;
}

export function useOpportunity(id: string, config: UseArbitrageConfig = {}) {
  const { realtime = false, enabled = true } = config;
  const queryClient = useQueryClient();

  const query = useQuery({
    ...arbitrageQueries.opportunity(id),
    enabled: enabled && !!id,
  });

  // Setup real-time updates for specific opportunity
  useEffect(() => {
    if (!realtime || !enabled || !id) return;

    const unsubscribe = wsClient.subscribeToArbitrageUpdates((message) => {
      if (message.data.id === id) {
        queryClient.invalidateQueries({ 
          queryKey: arbitrageQueries.opportunity(id).queryKey 
        });
        
        // Optimistic update
        queryClient.setQueryData(
          arbitrageQueries.opportunity(id).queryKey,
          (oldData: unknown) => {
            if (oldData && typeof oldData === 'object' && oldData !== null) {
              const typedOldData = oldData as { data?: Record<string, unknown> };
              return {
                ...typedOldData,
                data: { ...typedOldData.data, ...message.data }
              };
            }
            return oldData;
          }
        );
      }
    });

    return unsubscribe;
  }, [realtime, enabled, id, queryClient]);

  return query;
}

export function useExecution(id: string, config: UseArbitrageConfig = {}) {
  const { enabled = true } = config; // Default to enabled for executions
  
  const query = useQuery({
    ...arbitrageQueries.execution(id),
    enabled: enabled && !!id,
  });

  // For now, executions are considered completed immediately
  // In a real implementation, you'd track execution status separately
  const isActive = useMemo(() => {
    return false; // No active status tracking for now
  }, []);

  return {
    ...query,
    isActive,
    isCompleted: query.data?.data?.success === true,
    isFailed: query.data?.data?.success === false,
  };
}

export function useTradeHistory(
  filters?: TradeHistoryFilters,
  config: UseArbitrageConfig = {}
) {
  const { enabled = true } = config;

  return useQuery({
    ...arbitrageQueries.history(filters),
    enabled,
  });
}

export function useTradeHistoryInfinite(
  filters?: TradeHistoryFilters,
  config: UseArbitrageConfig = {}
) {
  const { enabled = true } = config;

  return useInfiniteQuery({
    ...arbitrageQueries.historyInfinite(filters),
    enabled,
    initialPageParam: 1, // Add initialPageParam
  });
}

// === System Health Hooks ===

export function useSystemHealth(config: UseArbitrageConfig = {}) {
  const { realtime = true, enabled = true } = config;
  const queryClient = useQueryClient();

  const query = useQuery({
    ...systemQueries.health(),
    enabled,
    refetchInterval: realtime ? 30000 : false, // 30 seconds
  });

  // Setup real-time updates
  useEffect(() => {
    if (!realtime || !enabled) return;

    const unsubscribe = wsClient.subscribeToSystemHealthUpdates(() => {
      queryClient.invalidateQueries({ 
        queryKey: systemQueries.health().queryKey 
      });
    });

    return unsubscribe;
  }, [realtime, enabled, queryClient]);

  return query;
}

export function useCircuitBreaker(config: UseArbitrageConfig = {}) {
  const { realtime = true, enabled = true } = config;
  const queryClient = useQueryClient();

  const query = useQuery({
    ...systemQueries.circuitBreaker(),
    enabled,
    refetchInterval: realtime ? 30000 : false, // 🚀 OPTIMIZED: 30 seconds instead of 10 (WebSocket handles real-time updates)
    staleTime: 15000, // 🚀 NEW: Consider data stale after 15s for circuit breakers
  });

  // Setup real-time updates
  useEffect(() => {
    if (!realtime || !enabled) return;

    const unsubscribe = wsClient.subscribeToCircuitBreakerUpdates(() => {
      queryClient.invalidateQueries({ 
        queryKey: systemQueries.circuitBreaker().queryKey 
      });
    });

    return unsubscribe;
  }, [realtime, enabled, queryClient]);

  return {
    ...query,
    isActive: query.data?.data?.isActive || false,
    triggeredRules: query.data?.data?.triggeredRules || [],
  };
}

export function useTradingStats(timeframe?: string, config: UseArbitrageConfig = {}) {
  const { enabled = true } = config;

  return useQuery({
    ...systemQueries.stats(timeframe),
    enabled,
  });
}

export function useTradingConfig(config: UseArbitrageConfig = {}) {
  const { enabled = true } = config;

  return useQuery({
    ...systemQueries.config(),
    enabled,
  });
}

// === Mutation Hooks ===

export function useExecuteArbitrage() {
  const queryClient = useQueryClient();

  return useMutation({
    ...arbitrageMutations.execute(),
    onSuccess: (_, variables) => {
      // Invalidate and refetch opportunities
      queryClient.invalidateQueries({ queryKey: ['arbitrage', 'opportunities'] });
      
      // Invalidate specific opportunity
      queryClient.invalidateQueries({ 
        queryKey: ['arbitrage', 'opportunities', variables] 
      });
      
      // Refetch executions for this opportunity
      queryClient.invalidateQueries({ 
        queryKey: ['arbitrage', 'executions'] 
      });
    },
  });
}

export function useTriggerScan() {
  const queryClient = useQueryClient();

  return useMutation({
    ...arbitrageMutations.triggerScan(),
    onSuccess: () => {
      // Invalidate opportunities after manual scan
      queryClient.invalidateQueries({ queryKey: ['arbitrage', 'opportunities'] });
    },
  });
}

export function useResetCircuitBreaker() {
  const queryClient = useQueryClient();

  return useMutation({
    ...systemMutations.resetCircuitBreaker(),
    onSuccess: () => {
      // Invalidate circuit breaker status
      queryClient.invalidateQueries({ queryKey: ['system', 'circuit-breaker'] });
    },
  });
}

export function useUpdateConfig() {
  const queryClient = useQueryClient();

  return useMutation({
    ...systemMutations.updateConfig(),
    onSuccess: () => {
      // Invalidate config after update
      queryClient.invalidateQueries({ queryKey: ['system', 'config'] });
    },
  });
}

// === WebSocket Hooks ===

export function useWebSocketConnection() {
  const [connectionState, setConnectionState] = useState<ConnectionState>(
    wsClient.getConnectionState()
  );
  const [connectionHealth, setConnectionHealth] = useState(
    wsClient.getConnectionHealth()
  );

  useEffect(() => {
    const unsubscribe = wsClient.subscribeToConnectionState(setConnectionState);
    
    // Update health info periodically
    const interval = setInterval(() => {
      setConnectionHealth(wsClient.getConnectionHealth());
    }, 5000);

    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, []);

  const connect = useCallback(() => wsClient.connect(), []);
  const disconnect = useCallback(() => wsClient.disconnect(), []);
  const reconnect = useCallback(() => wsClient.reconnect(), []);

  return {
    connectionState,
    connectionHealth,
    isConnected: connectionState === ConnectionState.CONNECTED,
    isConnecting: connectionState === ConnectionState.CONNECTING,
    isReconnecting: connectionState === ConnectionState.RECONNECTING,
    isFailed: connectionState === ConnectionState.FAILED,
    connect,
    disconnect,
    reconnect,
  };
}

export function useWebSocketMessages() {
  const [messages, setMessages] = useState<Record<string, unknown>[]>([]);
  const [lastMessage, setLastMessage] = useState<Record<string, unknown> | null>(null);

  useEffect(() => {
    const unsubscribe = wsClient.on('message', (message) => {
      const typedMessage = message as Record<string, unknown>;
      setLastMessage(typedMessage);
      setMessages(prev => [...prev.slice(-99), typedMessage]); // Keep last 100 messages
    });

    return unsubscribe;
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setLastMessage(null);
  }, []);

  return {
    messages,
    lastMessage,
    messageCount: messages.length,
    clearMessages,
  };
}

// === Combined Dashboard Hook ===

export function useDashboardData(config: UseArbitrageConfig = {}) {
  const { realtime = true, enabled = true } = config;

  const opportunities = useOpportunities(undefined, { realtime, enabled });
  const health = useSystemHealth({ realtime, enabled });
  const circuitBreaker = useCircuitBreaker({ realtime, enabled });
  const stats = useTradingStats(undefined, { enabled });
  const connection = useWebSocketConnection();

  const isLoading = opportunities.isLoading || health.isLoading || 
                   circuitBreaker.isLoading || stats.isLoading;
  
  const isError = opportunities.isError || health.isError || 
                 circuitBreaker.isError || stats.isError;

  const activeOpportunities = useMemo(() => {
    return opportunities.data?.data?.data?.filter(
      opp => opp.status === 'ACTIVE'
    ) || [];
  }, [opportunities.data]);

  const totalProfit = useMemo(() => {
    return activeOpportunities.reduce((sum, opp) => sum + opp.netProfit, 0);
  }, [activeOpportunities]);

  const opportunitiesData = useMemo(() => {
    return opportunities.data?.data?.data || [];
  }, [opportunities.data]);

  return {
    // Data
    opportunities: opportunitiesData,
    activeOpportunities,
    health: health.data?.data,
    circuitBreaker: circuitBreaker.data?.data,
    stats: stats.data?.data,
    
    // Computed values
    totalProfit,
    opportunityCount: activeOpportunities.length,
    systemHealthy: health.data?.data?.status === 'HEALTHY',
    circuitBreakerActive: circuitBreaker.data?.data?.isActive || false,
    
    // Loading states
    isLoading,
    isError,
    
    // Connection info
    connection,
    
    // Refetch functions
    refetchAll: () => {
      opportunities.refetch();
      health.refetch();
      circuitBreaker.refetch();
      stats.refetch();
    },
  };
}

// === Real-time Event Hooks ===

export function useArbitrageEvents() {
  const [newOpportunity, setNewOpportunity] = useState<ArbitrageOpportunity | null>(null);
  const [updatedOpportunity, setUpdatedOpportunity] = useState<Partial<ArbitrageOpportunity> | null>(null);

  useEffect(() => {
    const unsubOpportunity = wsClient.subscribeToArbitrageOpportunities((message) => {
      setNewOpportunity(message.data);
    });

    const unsubUpdate = wsClient.subscribeToArbitrageUpdates((message) => {
      setUpdatedOpportunity(message.data);
    });

    return () => {
      unsubOpportunity();
      unsubUpdate();
    };
  }, []);

  const clearEvents = useCallback(() => {
    setNewOpportunity(null);
    setUpdatedOpportunity(null);
  }, []);

  return {
    newOpportunity,
    updatedOpportunity,
    clearEvents,
  };
}

import { createAuthenticApiClient } from '@/lib/authenticApi';

const authenticApi = createAuthenticApiClient();

// === Trading History Hooks ===

export function useTradingHistory(filters?: TradeHistoryFilters) {
  return useQuery({
    queryKey: ['trading-history', filters],
    queryFn: () => authenticApi.getTradeHistory(filters),
    staleTime: 30000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useTradingMetrics(timeRange: TimeRange = '30d') {
  return useQuery({
    queryKey: ['trading-metrics', timeRange],
    queryFn: () => authenticApi.getPerformanceMetrics(timeRange),
    staleTime: 60000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useChartData(timeRange: TimeRange = '30d') {
  return useQuery({
    queryKey: ['chart-data', timeRange],
    queryFn: () => authenticApi.getChartData(timeRange),
    staleTime: 60000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useProviderPerformance() {
  return useQuery({
    queryKey: ['provider-performance'],
    queryFn: () => authenticApi.getProviderPerformance(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  });
}

export function useExportTrades() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (options: ExportOptions) => authenticApi.exportTrades(options),
    onSuccess: () => {
      // Optionally refresh related data
      queryClient.invalidateQueries({ queryKey: ['trading-history'] });
    },
  });
}

// Mock API removed - using authentic API client instead

// === Performance Monitoring Hook ===

export function usePerformanceMetrics() {
  const [metrics, setMetrics] = useState({
    queriesCount: 0,
    mutationsCount: 0,
    cacheHitRate: 0,
    averageQueryTime: 0,
  });

  const queryClient = useQueryClient();

  useEffect(() => {
    const updateMetrics = () => {
      const cache = queryClient.getQueryCache();
      const queries = cache.getAll();
      
      const successfulQueries = queries.filter(q => q.state.status === 'success');
      const cacheHitRate = queries.length > 0 ? successfulQueries.length / queries.length : 0;

      setMetrics({
        queriesCount: queries.length,
        mutationsCount: queryClient.getMutationCache().getAll().length,
        cacheHitRate,
        averageQueryTime: 0, // Would need to track this separately
      });
    };

    const interval = setInterval(updateMetrics, 10000); // Update every 10 seconds
    updateMetrics(); // Initial update

    return () => clearInterval(interval);
  }, [queryClient]);

  return metrics;
} 