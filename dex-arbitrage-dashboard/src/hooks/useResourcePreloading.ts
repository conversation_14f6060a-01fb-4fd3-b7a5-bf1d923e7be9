import { useEffect, useCallback, useRef } from 'react';

interface PreloadOptions {
  priority?: 'high' | 'low' | 'auto';
  crossOrigin?: 'anonymous' | 'use-credentials';
  type?: 'script' | 'style' | 'image' | 'font';
}

interface PreloadResource {
  href: string;
  as?: string;
  type?: string;
  crossOrigin?: string;
  media?: string;
}

export function useResourcePreloading() {
  const preloadedRef = useRef<Set<string>>(new Set());

  const preloadResource = useCallback((resource: PreloadResource) => {
    const { href, as, type, crossOrigin, media } = resource;
    
    // Skip if already preloaded
    if (preloadedRef.current.has(href)) {
      return;
    }

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    
    if (as) link.as = as;
    if (type) link.type = type;
    if (crossOrigin) link.crossOrigin = crossOrigin;
    if (media) link.media = media;

    document.head.appendChild(link);
    preloadedRef.current.add(href);

    // Cleanup function
    return () => {
      if (document.head.contains(link)) {
        document.head.removeChild(link);
      }
      preloadedRef.current.delete(href);
    };
  }, []);

  const preloadScript = useCallback((src: string, options?: PreloadOptions) => {
    return preloadResource({
      href: src,
      as: 'script',
      type: 'application/javascript',
      crossOrigin: options?.crossOrigin
    });
  }, [preloadResource]);

  const preloadStyle = useCallback((href: string, options?: PreloadOptions) => {
    return preloadResource({
      href,
      as: 'style',
      type: 'text/css',
      crossOrigin: options?.crossOrigin
    });
  }, [preloadResource]);

  const preloadImage = useCallback((src: string, options?: PreloadOptions) => {
    return preloadResource({
      href: src,
      as: 'image',
      crossOrigin: options?.crossOrigin
    });
  }, [preloadResource]);

  const preloadFont = useCallback((href: string, options?: PreloadOptions) => {
    return preloadResource({
      href,
      as: 'font',
      type: 'font/woff2',
      crossOrigin: options?.crossOrigin
    });
  }, [preloadResource]);

  const prefetchResource = useCallback((href: string) => {
    if (preloadedRef.current.has(href)) {
      return;
    }

    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;

    document.head.appendChild(link);
    preloadedRef.current.add(href);

    return () => {
      if (document.head.contains(link)) {
        document.head.removeChild(link);
      }
      preloadedRef.current.delete(href);
    };
  }, []);

  const preloadCriticalResources = useCallback(() => {
    // Preload critical CSS
    preloadStyle('/src/index.css');
    
    // Preload critical fonts
    preloadFont('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
    
    // Preload critical images
    preloadImage('/public/vite.svg');
  }, [preloadStyle, preloadFont, preloadImage]);

  const preloadRoute = useCallback((route: string) => {
    // Preload route-specific resources
    switch (route) {
      case '/opportunities':
        // Preload arbitrage features chunk
        prefetchResource('/src/hooks/useArbitrage.ts');
        prefetchResource('/src/components/features/OpportunityScanner.tsx');
        break;
      case '/dashboard':
        // Preload dashboard components
        prefetchResource('/src/components/Dashboard.tsx');
        break;
      default:
        break;
    }
  }, [prefetchResource]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Capture ref value to avoid cleanup warning
      // This ref contains a Set, not a React node
      // eslint-disable-next-line react-hooks/exhaustive-deps
      const preloaded = preloadedRef.current;
      preloaded.clear();
    };
  }, []);

  return {
    preloadResource,
    preloadScript,
    preloadStyle,
    preloadImage,
    preloadFont,
    prefetchResource,
    preloadCriticalResources,
    preloadRoute,
    isPreloaded: (href: string) => preloadedRef.current.has(href)
  };
}

// Hook for intersection observer-based preloading
export function useIntersectionPreloading(
  targetRef: React.RefObject<Element>,
  preloadFn: () => void,
  options?: IntersectionObserverInit
) {
  const hasPreloadedRef = useRef(false);

  useEffect(() => {
    if (!targetRef.current || hasPreloadedRef.current) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasPreloadedRef.current) {
            preloadFn();
            hasPreloadedRef.current = true;
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Start preloading 50px before element is visible
        ...options
      }
    );

    observer.observe(targetRef.current);

    return () => {
      observer.disconnect();
    };
  }, [targetRef, preloadFn, options]);

  return hasPreloadedRef.current;
}

// Hook for route-based preloading
export function useRoutePreloading() {
  const { preloadRoute } = useResourcePreloading();

  const preloadOnHover = useCallback((route: string) => {
    return () => {
      preloadRoute(route);
    };
  }, [preloadRoute]);

  const preloadOnFocus = useCallback((route: string) => {
    return () => {
      preloadRoute(route);
    };
  }, [preloadRoute]);

  return {
    preloadOnHover,
    preloadOnFocus
  };
} 