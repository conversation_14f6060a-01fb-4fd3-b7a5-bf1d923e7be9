// Terminal color schemes
export const TERMINAL_THEMES = {
  classic: {
    name: 'Classic Green',
    bg: '#0a0a0a',
    fg: '#00ff41',
    border: '#333',
    accent: '#ffff00',
    error: '#ff4444',
    warning: '#ffaa00',
    dim: '#888',
    blue: '#4499ff',
    description: 'Traditional green phosphor terminal'
  },
  amber: {
    name: 'Amber',
    bg: '#1a0f00',
    fg: '#ffb000',
    border: '#664400',
    accent: '#ffd700',
    error: '#ff6600',
    warning: '#ff8800',
    dim: '#996600',
    blue: '#ffcc66',
    description: 'Classic amber CRT monitor'
  },
  blue: {
    name: 'IBM Blue',
    bg: '#000033',
    fg: '#00aaff',
    border: '#003366',
    accent: '#66ccff',
    error: '#ff3366',
    warning: '#ffaa33',
    dim: '#6699cc',
    blue: '#99ddff',
    description: 'IBM mainframe terminal'
  },
  matrix: {
    name: 'Matrix Red',
    bg: '#0f0000',
    fg: '#ff0040',
    border: '#440011',
    accent: '#ff3366',
    error: '#ff6666',
    warning: '#ff9933',
    dim: '#aa0022',
    blue: '#ff6699',
    description: 'Matrix-style red terminal'
  },
  white: {
    name: 'Paper',
    bg: '#f5f5f5',
    fg: '#000000',
    border: '#cccccc',
    accent: '#0066cc',
    error: '#cc0000',
    warning: '#cc6600',
    dim: '#666666',
    blue: '#0099ff',
    description: 'High contrast paper mode'
  },
  purple: {
    name: 'Purple Hacker',
    bg: '#1a0033',
    fg: '#bb44ff',
    border: '#440088',
    accent: '#ff44bb',
    error: '#ff4466',
    warning: '#ffaa44',
    dim: '#8844aa',
    blue: '#6699ff',
    description: 'Cyberpunk purple terminal'
  },
  cyan: {
    name: 'Cyan Retro',
    bg: '#001a1a',
    fg: '#00ffff',
    border: '#004444',
    accent: '#44ffff',
    error: '#ff4444',
    warning: '#ffaa00',
    dim: '#008888',
    blue: '#66ddff',
    description: 'Retro cyan workstation'
  }
};

export type ThemeId = keyof typeof TERMINAL_THEMES; 