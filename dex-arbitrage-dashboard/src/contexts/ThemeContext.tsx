import React, { useState, useEffect } from 'react';
import { TERMINAL_THEMES, type ThemeId } from './theme-constants';
import { ThemeContext } from './theme-context';

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [currentTheme, setCurrentTheme] = useState<ThemeId>(() => {
    const saved = localStorage.getItem('terminal-theme');
    return (saved as ThemeId) || 'classic';
  });

  const setTheme = (themeId: ThemeId) => {
    setCurrentTheme(themeId);
    localStorage.setItem('terminal-theme', themeId);
    applyThemeToDocument(TERMINAL_THEMES[themeId]);
  };

  useEffect(() => {
    applyThemeToDocument(TERMINAL_THEMES[currentTheme]);
  }, [currentTheme]);

  const value = {
    currentTheme,
    setTheme,
    themes: TERMINAL_THEMES,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

function applyThemeToDocument(theme: typeof TERMINAL_THEMES[ThemeId]) {
  const root = document.documentElement;
  
  // Apply CSS custom properties
  root.style.setProperty('--terminal-bg', theme.bg);
  root.style.setProperty('--terminal-fg', theme.fg);
  root.style.setProperty('--terminal-border', theme.border);
  root.style.setProperty('--terminal-accent', theme.accent);
  root.style.setProperty('--terminal-error', theme.error);
  root.style.setProperty('--terminal-warning', theme.warning);
  root.style.setProperty('--terminal-dim', theme.dim);
  root.style.setProperty('--terminal-blue', theme.blue);
}

 