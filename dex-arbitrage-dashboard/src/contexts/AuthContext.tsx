import React, { useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import { AuthContext, type User, type AuthContextType } from './auth-context';

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    // Check for existing session
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        if (token) {
          // In a real app, you'd validate the token with your backend
          // For now, we'll simulate a user
          setToken(token);
          setUser({
            id: '1',
            email: '<EMAIL>',
            name: 'Demo User',
            role: 'trader',
            permissions: ['view', 'trade', 'settings']
          });
        }
      } catch (error) {
        console.error('Auth check failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => { // eslint-disable-line @typescript-eslint/no-unused-vars
    setIsLoading(true);
    try {
      // In a real app, you'd make an API call here
      // For now, we'll simulate a successful login
      const mockUser: User = {
        id: '1',
        email,
        name: 'Demo User',
        role: 'trader',
        permissions: ['view', 'trade', 'settings']
      };
      
      const mockToken = 'mock_token';
      localStorage.setItem('auth_token', mockToken);
      setToken(mockToken);
      setUser(mockUser);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      localStorage.removeItem('auth_token');
      setToken(null);
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (email: string, password: string, name: string) => {
    setIsLoading(true);
    try {
      // In a real app, you'd make an API call here
      // For now, we'll simulate a successful signup
      const mockUser: User = {
        id: '1',
        email,
        name,
        role: 'viewer',
        permissions: ['view']
      };
      
      const mockToken = 'mock_token';
      localStorage.setItem('auth_token', mockToken);
      setToken(mockToken);
      setUser(mockUser);
    } catch (error) {
      console.error('Signup failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    login,
    logout,
    signup,
    isAuthenticated: !!user,
    token,
    hasRole: (role: string) => user?.role === role || user?.permissions.includes(role) || false
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
} 