// === Core Trading Types ===

export interface Token {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
  tags?: string[];
}

export interface TradingPair {
  baseToken: Token;
  quoteToken: Token;
  address: string;
}

export interface MarketData {
  price: number;
  volume24h: number;
  priceChange24h: number;
  priceChangePercent24h: number;
  liquidity: number;
  lastUpdated: string; // ISO timestamp
}

// === Arbitrage Types ===

export interface ArbitrageOpportunity {
  id: string;
  pair: TradingPair;
  buyExchange: string;
  sellExchange: string;
  buyPrice: number;
  sellPrice: number;
  profitAmount: number;
  profitPercent: number;
  volume: number;
  confidence: number; // 0-1 confidence score
  estimatedGas: number;
  netProfit: number; // profit after gas
  timeToExpire: number; // seconds
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  status: 'ACTIVE' | 'EXECUTING' | 'EXPIRED' | 'ERROR';
  createdAt: string;
  updatedAt: string;
}

export interface ArbitrageExecution {
  id: string;
  opportunityId: string;
  status: 'PENDING' | 'EXECUTING' | 'COMPLETED' | 'FAILED';
  startTime: string;
  endTime?: string;
  transactions: {
    buy?: {
      hash: string;
      status: 'PENDING' | 'CONFIRMED' | 'FAILED';
    };
    sell?: {
      hash: string;
      status: 'PENDING' | 'CONFIRMED' | 'FAILED';
    };
  };
  actualProfit?: number;
  gasUsed?: number;
  error?: string;
}

// === Circuit Breaker Types ===

export interface CircuitBreakerRule {
  id: string;
  name: string;
  description: string;
  type: 'DAILY_LOSS' | 'POSITION_SIZE' | 'BALANCE_THRESHOLD' | 'ERROR_RATE';
  threshold: number;
  currentValue: number;
  enabled: boolean;
  lastTriggered?: string;
}

export interface CircuitBreakerStatus {
  isActive: boolean;
  triggeredRules: CircuitBreakerRule[];
  dailyLoss: number;
  dailyLossLimit: number;
  positionSize: number;
  positionSizeLimit: number;
  errorRate: number;
  errorRateLimit: number;
  lastReset: string;
  autoResetTime?: string;
}

// === System Health Types ===

export interface ProviderHealth {
  name: string;
  status: 'HEALTHY' | 'DEGRADED' | 'DOWN';
  latency: number; // ms
  successRate: number; // 0-1
  lastPing: string;
  errorMessage?: string;
  uptime: number; // seconds
}

export interface SystemHealth {
  overall: 'HEALTHY' | 'DEGRADED' | 'DOWN';
  providers: ProviderHealth[];
  database: {
    status: 'CONNECTED' | 'DISCONNECTED';
    latency: number;
  };
  websocket: {
    status: 'CONNECTED' | 'DISCONNECTED';
    connections: number;
  };
  memoryUsage: number; // percentage
  cpuUsage: number; // percentage
  lastUpdated: string;
}

// === WebSocket Message Types ===

export type WebSocketMessageType = 
  | 'ARBITRAGE_OPPORTUNITY' 
  | 'ARBITRAGE_UPDATE' 
  | 'ARBITRAGE_EXECUTION'
  | 'CIRCUIT_BREAKER_UPDATE'
  | 'SYSTEM_HEALTH_UPDATE'
  | 'PROVIDER_STATUS_UPDATE'
  | 'HEARTBEAT'
  | 'ERROR';

export interface WebSocketMessage<T = unknown> {
  type: WebSocketMessageType;
  timestamp: string;
  data: T;
  id?: string;
}

export interface ArbitrageOpportunityMessage extends WebSocketMessage<ArbitrageOpportunity> {
  type: 'ARBITRAGE_OPPORTUNITY';
}

export interface ArbitrageUpdateMessage extends WebSocketMessage<Partial<ArbitrageOpportunity>> {
  type: 'ARBITRAGE_UPDATE';
  data: Partial<ArbitrageOpportunity> & { id: string };
}

export interface CircuitBreakerUpdateMessage extends WebSocketMessage<CircuitBreakerStatus> {
  type: 'CIRCUIT_BREAKER_UPDATE';
}

export interface SystemHealthUpdateMessage extends WebSocketMessage<SystemHealth> {
  type: 'SYSTEM_HEALTH_UPDATE';
}

export interface HeartbeatMessage extends WebSocketMessage<{ timestamp: string }> {
  type: 'HEARTBEAT';
}

export interface ErrorMessage extends WebSocketMessage<{ message: string; code?: string }> {
  type: 'ERROR';
}

// === API Response Types ===

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: unknown;
  };
  timestamp: string;
}

// === Query Parameters ===

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface ArbitrageFilters extends PaginationParams {
  minProfit?: number;
  maxRisk?: 'LOW' | 'MEDIUM' | 'HIGH';
  exchange?: string;
  tokenSymbol?: string;
  status?: 'ACTIVE' | 'EXECUTING' | 'EXPIRED' | 'ERROR';
  sortBy?: 'profit' | 'volume' | 'created' | 'confidence';
  sortOrder?: 'asc' | 'desc';
}

export interface TradeHistoryFilters extends PaginationParams {
  startDate?: string;
  endDate?: string;
  status?: 'COMPLETED' | 'FAILED';
  minProfit?: number;
  tokenSymbol?: string;
}

// === Configuration Types ===

export interface TradingConfig {
  maxPositionSize: number;
  minProfitThreshold: number;
  maxSlippage: number;
  gasPrice: number;
  enableAutoTrading: boolean;
  riskTolerance: 'CONSERVATIVE' | 'MODERATE' | 'AGGRESSIVE';
}

export interface NotificationSettings {
  enablePush: boolean;
  enableEmail: boolean;
  profitThreshold: number;
  lossThreshold: number;
  systemAlerts: boolean;
}

// === Statistics Types ===

export interface TradingStats {
  totalTrades: number;
  successfulTrades: number;
  failedTrades: number;
  totalProfit: number;
  totalLoss: number;
  netProfit: number;
  winRate: number;
  averageProfit: number;
  largestWin: number;
  largestLoss: number;
  tradingVolume: number;
  period: {
    start: string;
    end: string;
  };
}

export interface ProviderStats {
  name: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  uptime: number;
  lastError?: {
    message: string;
    timestamp: string;
  };
}

// === Real-time Data Types ===

export interface LivePrice {
  tokenAddress: string;
  symbol: string;
  price: number;
  volume24h: number;
  change24h: number;
  timestamp: string;
}

export interface OrderBookUpdate {
  exchange: string;
  pair: string;
  bids: Array<[number, number]>; // [price, size]
  asks: Array<[number, number]>; // [price, size]
  timestamp: string;
}

// === Trading History Types ===

export interface TradeRecord {
  id: string;
  executionId: string;
  opportunityId: string;
  pair: TradingPair;
  type: 'ARBITRAGE' | 'DIRECT';
  buyExchange: string;
  sellExchange: string;
  buyPrice: number;
  sellPrice: number;
  volume: number;
  grossProfit: number;
  netProfit: number;
  gasUsed: number;
  gasCost: number;
  slippage: number;
  executionTime: number; // milliseconds
  status: 'COMPLETED' | 'FAILED' | 'PARTIAL';
  startTime: string;
  endTime: string;
  error?: string;
  metadata?: {
    confidence: number;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    marketConditions?: string;
  };
}

export interface PerformanceMetrics {
  totalTrades: number;
  successRate: number;
  totalProfit: number;
  totalLoss: number;
  netProfit: number;
  averageProfit: number;
  largestWin: number;
  largestLoss: number;
  totalVolume: number;
  averageExecutionTime: number;
  profitFactor: number; // gross profit / gross loss
  sharpeRatio: number;
  maxDrawdown: number;
  winStreak: number;
  lossStreak: number;
  profitPerTrade: number;
  roi: number; // return on investment percentage
}

export interface ProviderPerformance {
  name: string;
  totalTrades: number;
  successRate: number;
  averageLatency: number;
  totalProfit: number;
  profitPerTrade: number;
  reliability: number; // 0-1
  uptime: number; // percentage
  lastUsed: string;
  trending: 'UP' | 'DOWN' | 'STABLE';
}

export interface ProfitLossPoint {
  timestamp: string;
  cumulativeProfit: number;
  dailyProfit: number;
  tradeCount: number;
  winRate: number;
}

export interface VolumePoint {
  timestamp: string;
  volume: number;
  tradeCount: number;
  avgTradeSize: number;
}

export interface ChartData {
  profitLoss: ProfitLossPoint[];
  volume: VolumePoint[];
  performance: PerformanceMetrics;
  providerStats: ProviderPerformance[];
}

export interface ExportOptions {
  format: 'CSV' | 'JSON' | 'EXCEL';
  dateRange: {
    start: string;
    end: string;
  };
  includeMetadata: boolean;
  includeCharts: boolean;
}

// === Utility Types ===

export type TimeRange = '1m' | '5m' | '15m' | '1h' | '4h' | '1d' | '7d' | '30d';

export type OrderSide = 'BUY' | 'SELL';

export type ExchangeName = 'jupiter' | 'orca' | 'meteora' | 'raydium' | 'serum';

// === Type Guards ===

export const isArbitrageOpportunity = (obj: unknown): obj is ArbitrageOpportunity => {
  return obj !== null && 
         typeof obj === 'object' && 
         'id' in obj && 
         typeof (obj as { id: unknown }).id === 'string' && 
         'profitPercent' in obj && 
         typeof (obj as { profitPercent: unknown }).profitPercent === 'number';
};

export const isWebSocketMessage = (obj: unknown): obj is WebSocketMessage => {
  return obj !== null && 
         typeof obj === 'object' && 
         'type' in obj && 
         typeof (obj as { type: unknown }).type === 'string' && 
         'timestamp' in obj && 
         typeof (obj as { timestamp: unknown }).timestamp === 'string';
};

export const isApiError = (obj: unknown): obj is ApiError => {
  return obj !== null && 
         typeof obj === 'object' && 
         'success' in obj && 
         (obj as { success: unknown }).success === false && 
         'error' in obj;
};

// === Constants ===

export const EXCHANGE_NAMES: ExchangeName[] = ['jupiter', 'orca', 'meteora', 'raydium', 'serum'];

export const RISK_LEVELS = ['LOW', 'MEDIUM', 'HIGH'] as const;

export const OPPORTUNITY_STATUSES = ['ACTIVE', 'EXECUTING', 'EXPIRED', 'ERROR'] as const;

export const EXECUTION_STATUSES = ['PENDING', 'EXECUTING', 'COMPLETED', 'FAILED'] as const; 