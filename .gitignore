# Prism MEV Trading Platform - .gitignore
# Comprehensive ignore patterns for Python, trading, and development

# =============================================================================
# SECURITY & SENSITIVE DATA - CRITICAL TO IGNORE
# =============================================================================

# Environment and configuration files with sensitive data
*.env
*.env.*
.env
.env.local
.env.production
.env.staging
.env.development
config.env
.config
secret.json
secrets.json
*.key
*.pem
*.p12
*.pfx

# Wallet and trading secrets
*wallet*
*private_key*
*seed_phrase*
*mnemonic*
*.wallet
*.keystore

# API keys and credentials
*api_key*
*secret_key*
*access_token*
*auth_token*
credentials/
auth/

# Trading and financial data
trading_logs/
trade_history/
position_data/
portfolio_data/
profit_loss/
*.trade
*.position
*.pnl

# =============================================================================
# PYTHON
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
# Python lib folders (but not frontend src/lib/)
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# =============================================================================
# FRONTEND - ALLOW src/lib (application code)
# =============================================================================
# Note: src/lib/ contains application code and should NOT be ignored
# Only ignore Python lib/ folders, not frontend src/lib/

# Explicitly allow frontend src/lib folder
!dex-arbitrage-dashboard/src/lib/
!src/lib/

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
.augment/
.augment
.augment/**.md
.augment/** */

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# MEV & TRADING SPECIFIC
# =============================================================================

# MEV bot data and logs
mev_logs/
arbitrage_logs/
flashloan_logs/
sandwich_logs/
frontrun_logs/

# Trading data and analytics
backtest_results/
performance_metrics/
market_data/
price_feeds/
orderbook_data/
*.tick
*.ohlc
*.trade_data

# Blockchain and DeFi data
blockchain_cache/
transaction_cache/
block_data/
mempool_data/
*.block
*.tx

# Strategy and algorithm data
strategy_cache/
algorithm_cache/
model_cache/
ml_models/
*.model
*.pkl
*.joblib

# Provider-specific caches
dex_cache/
cex_cache/
provider_cache/

# =============================================================================
# DEVELOPMENT & TOOLS
# =============================================================================

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Cursor IDE
.cursor/settings.json
.cursor/workspaceStorage/

# Debug files
*.debug
debug/
*.trace

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup

# Log files
logs/
*.log
*.log.*
log_*

# Data files (unless specifically needed)
data/
datasets/
*.csv
*.json.gz
*.parquet

# Cache directories
.cache/
cache/
__cache__/

# =============================================================================
# SYSTEM & OS
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# =============================================================================
# DOCKER & CONTAINERS
# =============================================================================

# Docker
Dockerfile.local
docker-compose.override.yml
.dockerignore

# =============================================================================
# DATABASE & STORAGE
# =============================================================================

# SQLite databases
*.db
*.sqlite
*.sqlite3

# Database dumps
*.sql
*.dump

# Vector databases
qdrant_storage/
chromadb/
pinecone_cache/

# =============================================================================
# MONITORING & METRICS
# =============================================================================

# Prometheus metrics
prometheus_data/
metrics_cache/

# Grafana
grafana_data/

# Application performance monitoring
apm_data/
traces/

# =============================================================================
# DOCUMENTATION & ASSETS
# =============================================================================

# Generated documentation
docs/build/
docs/_build/
site/

# Asset compilation
assets/dist/
static/dist/

# =============================================================================
# PROJECT SPECIFIC EXCLUSIONS
# =============================================================================

# Keep important config templates but ignore actual configs
!config.template.env
!example.env

# Keep test fixtures but ignore test outputs
!tests/fixtures/
tests/output/
tests/results/

# Keep documentation source but ignore builds
!docs/source/
docs/build/

# Keep scripts but ignore their outputs
!scripts/
scripts/output/
scripts/logs/
